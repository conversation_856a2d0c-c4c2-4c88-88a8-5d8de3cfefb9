# OpenVPN客户端配置
# 服务器: *************:1195
# 实例: proxy-newyork-0812
# 生成时间: Tue Aug 12 08:22:40 AM UTC 2025

client
dev tun
proto udp
remote ************* 1195
resolv-retry infinite
nobind
persist-key
persist-tun
remote-cert-tls server
auth SHA512
cipher AES-256-GCM
ignore-unknown-option block-outside-dns
block-outside-dns
verb 3

<ca>
-----BEGIN CERTIFICATE-----
MIIDSzCCAjOgAwIBAgIUSCz6a8xVEf9oatD/mcRqs1oMc80wDQYJKoZIhvcNAQEL
BQAwFjEUMBIGA1UEAwwLRWFzeS1SU0EgQ0EwHhcNMjUwODEyMDgyMjM3WhcNMzUw
ODEwMDgyMjM3WjAWMRQwEgYDVQQDDAtFYXN5LVJTQSBDQTCCASIwDQYJKoZIhvcN
AQEBBQADggEPADCCAQoCggEBAMzGmNsZOawRDWSVLO0kkNUkkRDTQvUiwm0LsDpW
m5LXZ1dbCh5P1xuPxeXmNF0AinLCE+lT+U7sMhYpKkryfzg/CgJvrhMQISeI7iog
KUH2wzqtbfZK5PEahKVhvTTFbpzD/LstaXX0nuPbSvpnQGEoP1hRgdYIy0/7hk8Z
2pxVdfNWqI1/GAS1owyaKMXrPmT0js32pmR4sxwjvvloAn+pmGWC7FEWsOXuPnza
J5hvSn3mP2mUJCQ6T0lO6hP4smiI7zI1LgSlDXo2VYVfvrob9rUoQF6q4jGwh+sB
+pkN+SO6H+Pz9hOI04jge/gy3KPngWYKYkkDFvvlX7OSlocCAwEAAaOBkDCBjTAM
BgNVHRMEBTADAQH/MB0GA1UdDgQWBBQ9swaZjE2DIAOKiEeB9UuK9WxMyTBRBgNV
HSMESjBIgBQ9swaZjE2DIAOKiEeB9UuK9WxMyaEapBgwFjEUMBIGA1UEAwwLRWFz
eS1SU0EgQ0GCFEgs+mvMVRH/aGrQ/5nEarNaDHPNMAsGA1UdDwQEAwIBBjANBgkq
hkiG9w0BAQsFAAOCAQEArZmCqUOJE5iRX1VwYsPcdxXzbb4e16UmE//vstZ+t8Im
ZQ/rjgz/0S3GBFdSYhqKThZulKPXs+RVWrDoQR7mg/PYKcuZUsiX55B/M/ALfOUf
tbuu0Pu/bT6f61In8hEF3XAjTSvBLRhI+qC5jQVx9bHPPfn3yzHtVJv9gdpP0i2R
6lCq9FzUCBgLM3GsieNbBECKb5+lSnK9MfyxxNGCQ3zAZT+CXCEh9b541hmet9wM
KZyDejtL4V5rh+c/qjQOzZWSmQq0/cKvDgVMe6cIxJTTAEFEO5T0hXX10+g17qMI
prfM9M4bN5O78wb0Zo3opWxeoJrP77i82AQN/ma5mg==
-----END CERTIFICATE-----
</ca>

<cert>
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number:
            74:27:a9:85:46:18:49:02:bf:61:9e:eb:49:e9:60:0e
        Signature Algorithm: sha256WithRSAEncryption
        Issuer: CN=Easy-RSA CA
        Validity
            Not Before: Aug 12 08:22:38 2025 GMT
            Not After : Aug 10 08:22:38 2035 GMT
        Subject: CN=client-proxy-newyork-0812
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (2048 bit)
                Modulus:
                    00:e3:3a:1f:87:f0:98:b5:bb:fc:e1:e9:19:83:05:
                    7c:ff:09:78:5c:2d:5a:ed:0d:af:8f:0a:25:20:4e:
                    40:23:42:00:42:9f:ce:d5:00:d9:0a:b5:49:ca:a0:
                    65:84:c5:05:70:8f:8b:0c:65:42:c9:ee:a4:64:38:
                    ab:24:6d:4e:9d:de:3d:45:6e:eb:21:d0:08:59:03:
                    1a:b2:ee:4e:2b:b2:83:83:f9:3f:13:a7:bd:4f:3a:
                    8c:24:fb:d4:5c:df:2c:b1:03:a9:52:cc:68:65:86:
                    6a:33:10:cd:1d:76:8b:4c:3f:f8:41:67:e1:08:07:
                    ec:36:af:62:b8:69:04:4f:a5:de:83:53:8e:5f:0a:
                    16:72:ea:2a:dc:d3:33:40:6e:e1:42:fc:56:2f:20:
                    f9:2f:d7:d6:36:e5:7c:ee:2a:33:a9:7c:eb:02:ad:
                    03:21:fa:95:94:2e:eb:bf:60:be:fe:2c:14:74:4f:
                    9d:6d:81:ae:9f:a4:47:7f:31:c9:92:a9:fc:ac:10:
                    43:96:99:eb:c5:d1:c4:bd:fd:2f:59:97:72:d9:ef:
                    8a:5a:7a:a8:8b:96:ce:f2:22:46:0c:d5:ac:0e:6f:
                    e3:8f:0b:2e:55:47:dd:e5:3e:95:a6:6f:c6:eb:34:
                    55:9b:50:a4:e7:04:73:0d:2c:1e:05:89:65:c8:70:
                    74:a7
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Basic Constraints: 
                CA:FALSE
            X509v3 Subject Key Identifier: 
                55:7C:E4:37:99:26:CC:BC:76:B6:AF:97:4D:6A:F0:60:89:E3:79:60
            X509v3 Authority Key Identifier: 
                keyid:3D:B3:06:99:8C:4D:83:20:03:8A:88:47:81:F5:4B:8A:F5:6C:4C:C9
                DirName:/CN=Easy-RSA CA
                serial:48:2C:FA:6B:CC:55:11:FF:68:6A:D0:FF:99:C4:6A:B3:5A:0C:73:CD
            X509v3 Extended Key Usage: 
                TLS Web Client Authentication
            X509v3 Key Usage: 
                Digital Signature
    Signature Algorithm: sha256WithRSAEncryption
    Signature Value:
        6e:cd:8a:d7:61:ca:60:ee:98:9a:ab:ef:f8:ea:28:1c:67:59:
        90:20:db:68:41:d3:39:9a:9e:a2:25:c2:a7:90:a1:af:65:0a:
        6d:94:fa:28:8d:6e:ad:ae:e7:25:99:8b:5e:e8:8f:e7:ae:a0:
        e8:14:1f:33:7c:5d:35:34:63:3b:d7:bc:c5:47:fb:ab:f8:74:
        e2:15:60:f2:95:10:8b:d6:e9:8f:32:88:ba:f2:3c:12:1a:ce:
        8e:bf:1d:57:91:54:8b:43:a5:66:cc:d3:c0:ab:d0:99:40:a3:
        69:09:34:54:ce:56:ef:5e:bd:57:be:6e:c4:c6:8a:09:14:f3:
        a0:cf:0c:c9:59:ac:b7:3b:df:82:e2:80:e8:e4:65:79:25:d5:
        f9:bd:76:ac:75:dc:d0:c2:10:07:bc:b7:13:60:0f:a3:5f:8f:
        b8:90:95:5a:bf:6f:e4:8d:ca:5b:21:18:76:86:98:b6:17:63:
        91:77:a8:39:24:e1:f2:2a:58:6f:ed:8e:0a:37:a7:3e:d0:d8:
        d5:48:a8:ee:b2:bd:77:ed:2a:be:60:33:93:db:17:7f:52:3f:
        2e:14:ef:c9:45:cd:cc:38:99:fb:50:b7:46:66:d9:ae:1f:c4:
        0c:87:4e:6c:1e:e1:1e:2e:c2:36:b2:05:e1:cc:23:54:fa:d1:
        29:c9:a4:a7
-----BEGIN CERTIFICATE-----
MIIDZzCCAk+gAwIBAgIQdCephUYYSQK/YZ7rSelgDjANBgkqhkiG9w0BAQsFADAW
MRQwEgYDVQQDDAtFYXN5LVJTQSBDQTAeFw0yNTA4MTIwODIyMzhaFw0zNTA4MTAw
ODIyMzhaMCQxIjAgBgNVBAMMGWNsaWVudC1wcm94eS1uZXd5b3JrLTA4MTIwggEi
MA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDjOh+H8Ji1u/zh6RmDBXz/CXhc
LVrtDa+PCiUgTkAjQgBCn87VANkKtUnKoGWExQVwj4sMZULJ7qRkOKskbU6d3j1F
bush0AhZAxqy7k4rsoOD+T8Tp71POowk+9Rc3yyxA6lSzGhlhmozEM0ddotMP/hB
Z+EIB+w2r2K4aQRPpd6DU45fChZy6irc0zNAbuFC/FYvIPkv19Y25XzuKjOpfOsC
rQMh+pWULuu/YL7+LBR0T51tga6fpEd/McmSqfysEEOWmevF0cS9/S9Zl3LZ74pa
eqiLls7yIkYM1awOb+OPCy5VR93lPpWmb8brNFWbUKTnBHMNLB4FiWXIcHSnAgMB
AAGjgaIwgZ8wCQYDVR0TBAIwADAdBgNVHQ4EFgQUVXzkN5kmzLx2tq+XTWrwYInj
eWAwUQYDVR0jBEowSIAUPbMGmYxNgyADiohHgfVLivVsTMmhGqQYMBYxFDASBgNV
BAMMC0Vhc3ktUlNBIENBghRILPprzFUR/2hq0P+ZxGqzWgxzzTATBgNVHSUEDDAK
BggrBgEFBQcDAjALBgNVHQ8EBAMCB4AwDQYJKoZIhvcNAQELBQADggEBAG7Nitdh
ymDumJqr7/jqKBxnWZAg22hB0zmanqIlwqeQoa9lCm2U+iiNbq2u5yWZi17oj+eu
oOgUHzN8XTU0YzvXvMVH+6v4dOIVYPKVEIvW6Y8yiLryPBIazo6/HVeRVItDpWbM
08Cr0JlAo2kJNFTOVu9evVe+bsTGigkU86DPDMlZrLc734LigOjkZXkl1fm9dqx1
3NDCEAe8txNgD6Nfj7iQlVq/b+SNylshGHaGmLYXY5F3qDkk4fIqWG/tjgo3pz7Q
2NVIqO6yvXftKr5gM5PbF39SPy4U78lFzcw4mftQt0Zm2a4fxAyHTmwe4R4uwjay
BeHMI1T60SnJpKc=
-----END CERTIFICATE-----
</cert>

<key>
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************</key>

<tls-crypt>
# Easy-RSA TLS-CRYPT Key
#
# 2048 bit OpenVPN static key
#
-----BEGIN OpenVPN Static key V1-----
1b81b3ccf3e08abc10c3abe4d87ca6d0
4571e2589637069b363d69f51b89d3e2
b48621d568ad3d7397e168d775b4d22e
96564fdd391f9ce20fddc24ea6e9650b
a05c38bf08056e129f0913c6ff298f55
790f73eb442efb2d27b4a27375920301
3b5f3ce3052ff8b980baa4de19c401c6
1b4946c5ef3c178216219f8313374ab0
7788a9ce22771f9be3db47425cc23fd2
5ec9eef06e740b45045cf85ee15b1933
017633f9ee8c5a9761090183bf851de0
4447ecb4683b62a772d06c7b614b1ef5
4d828413c8eacb7b65104e90149f718b
ec2bd331d011a52e7adee92df9ce47c0
d8d2b4bdaa522eb705fb650481c1de14
a94d53bf7b82aeb073222e0d575b202c
-----END OpenVPN Static key V1-----
</tls-crypt>
