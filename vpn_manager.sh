#!/bin/bash

# VPN管理脚本 - 专为OpenVPN 2.6.12设计
# 支持容器环境和DNS泄漏防护

set -euo pipefail

# 设置脚本权限
chmod +x "$0" 2>/dev/null || true

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置路径
VPN_BASE_DIR="/opt/vpn-client"
CONFIG_DIR="$VPN_BASE_DIR/configs/openvpn"
LOG_DIR="$VPN_BASE_DIR/logs"
PID_FILE="/var/run/openvpn-client.pid"

# 检查是否在容器中
IN_CONTAINER=false
if [[ -f /.dockerenv ]] || grep -q docker /proc/1/cgroup 2>/dev/null; then
    IN_CONTAINER=true
fi

# 显示菜单
show_menu() {
    echo -e "${BLUE}=== VPN管理器 ===${NC}"
    echo ""
    echo "1. 导入配置文件"
    echo "2. 连接VPN"
    echo "3. 断开VPN"
    echo "4. 查看连接状态"
    echo "5. 测试网络连接"
    echo "6. 查看日志"
    echo "7. DNS泄漏测试"
    echo "8. 系统诊断"
    echo "0. 退出"
    echo ""
}

# 导入配置文件
import_config() {
    echo -e "${CYAN}导入OpenVPN配置文件${NC}"
    echo ""
    
    read -p "请输入.ovpn文件的完整路径: " ovpn_path
    
    if [[ ! -f "$ovpn_path" ]]; then
        echo -e "${RED}错误: 文件不存在: $ovpn_path${NC}"
        return 1
    fi
    
    # 生成配置名称
    local config_name
    read -p "请输入配置名称 (默认: $(basename "$ovpn_path" .ovpn)): " config_name
    config_name=${config_name:-$(basename "$ovpn_path" .ovpn)}
    
    local target_config="$CONFIG_DIR/${config_name}.ovpn"
    
    # 复制配置文件
    cp "$ovpn_path" "$target_config"
    
    # 添加现代OpenVPN配置和DNS泄漏防护
    cat >> "$target_config" << 'EOF'

# 现代OpenVPN配置 - 自动添加
script-security 2
dhcp-option DNS *******
dhcp-option DNS *******
ignore-unknown-option block-outside-dns
block-outside-dns
redirect-gateway def1
route-delay 2
verb 3
persist-key
persist-tun
EOF
    
    # 容器环境特殊配置
    if [[ "$IN_CONTAINER" == true ]]; then
        cat >> "$target_config" << 'EOF'

# 容器环境适配
dev tun
dev-type tun
topology subnet
user nobody
group nogroup
EOF
    fi
    
    # 添加日志配置
    echo "log-append $LOG_DIR/${config_name}.log" >> "$target_config"
    
    echo -e "${GREEN}✓ 配置文件已导入: $target_config${NC}"
    echo -e "${GREEN}✓ 已添加DNS泄漏防护${NC}"
    
    # 设置为默认配置
    echo "$config_name" > "$VPN_BASE_DIR/default_config"
    echo -e "${GREEN}✓ 已设置为默认配置${NC}"
    
    return 0
}

# 连接VPN
connect_vpn() {
    echo -e "${CYAN}连接VPN${NC}"
    echo ""
    
    # 检查是否已连接
    if pgrep -f openvpn >/dev/null; then
        echo -e "${YELLOW}VPN已经连接${NC}"
        read -p "是否要断开当前连接并重新连接? (y/N): " reconnect
        if [[ $reconnect =~ ^[Yy]$ ]]; then
            disconnect_vpn
        else
            return 0
        fi
    fi
    
    # 列出可用配置
    local configs=($(find "$CONFIG_DIR" -name "*.ovpn" -exec basename {} .ovpn \; 2>/dev/null))
    
    if [[ ${#configs[@]} -eq 0 ]]; then
        echo -e "${RED}未找到配置文件${NC}"
        echo "请先导入配置文件"
        return 1
    fi
    
    echo "可用配置:"
    for i in "${!configs[@]}"; do
        echo "$((i+1)). ${configs[i]}"
    done
    echo ""
    
    # 选择配置
    local default_config=""
    if [[ -f "$VPN_BASE_DIR/default_config" ]]; then
        default_config=$(cat "$VPN_BASE_DIR/default_config")
    fi
    
    read -p "请选择配置编号 (默认: $default_config): " choice
    
    local config_name
    if [[ -z "$choice" && -n "$default_config" ]]; then
        config_name="$default_config"
    elif [[ "$choice" =~ ^[0-9]+$ ]] && [[ $choice -ge 1 && $choice -le ${#configs[@]} ]]; then
        config_name="${configs[$((choice-1))]}"
    else
        echo -e "${RED}无效选择${NC}"
        return 1
    fi
    
    local config_file="$CONFIG_DIR/${config_name}.ovpn"
    
    if [[ ! -f "$config_file" ]]; then
        echo -e "${RED}配置文件不存在: $config_file${NC}"
        return 1
    fi
    
    echo -e "${BLUE}正在连接VPN: $config_name${NC}"
    
    # 容器环境准备
    if [[ "$IN_CONTAINER" == true ]]; then
        # 确保TUN设备存在
        if [[ ! -c /dev/net/tun ]]; then
            mkdir -p /dev/net
            mknod /dev/net/tun c 10 200 2>/dev/null || true
            chmod 666 /dev/net/tun 2>/dev/null || true
        fi
        
        if [[ ! -c /dev/net/tun ]]; then
            echo -e "${RED}错误: TUN设备不可用${NC}"
            echo -e "${YELLOW}容器需要特权模式或--cap-add=NET_ADMIN --device=/dev/net/tun${NC}"
            return 1
        fi
    fi
    
    # 启动OpenVPN
    openvpn --config "$config_file" --daemon --writepid "$PID_FILE"
    
    if [[ $? -eq 0 ]]; then
        echo -e "${GREEN}VPN连接已启动${NC}"
        
        # 等待连接建立
        echo -e "${YELLOW}等待VPN连接建立...${NC}"
        local wait_count=0
        while [[ $wait_count -lt 30 ]]; do
            if check_vpn_connected; then
                echo -e "${GREEN}VPN连接成功！${NC}"
                show_connection_info
                return 0
            fi
            sleep 1
            ((wait_count++))
            echo -n "."
        done
        
        echo ""
        echo -e "${YELLOW}VPN连接超时，请检查配置和网络${NC}"
        return 1
    else
        echo -e "${RED}VPN连接失败${NC}"
        return 1
    fi
}

# 断开VPN
disconnect_vpn() {
    echo -e "${CYAN}断开VPN连接${NC}"
    echo ""
    
    # 停止OpenVPN进程
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill "$pid" 2>/dev/null; then
            echo -e "${GREEN}VPN进程已停止${NC}"
        fi
        rm -f "$PID_FILE"
    else
        # 强制停止所有OpenVPN进程
        pkill -f openvpn 2>/dev/null || true
        echo -e "${YELLOW}已强制停止OpenVPN进程${NC}"
    fi
    
    echo -e "${GREEN}VPN已断开${NC}"
}

# 检查VPN是否已连接
check_vpn_connected() {
    # 检查VPN接口是否存在且活跃
    local vpn_interface=$(ip link show | grep -E "tun[0-9]+|tap[0-9]+" | head -n1 | awk -F: '{print $2}' | tr -d ' ')
    
    if [[ -n "$vpn_interface" ]]; then
        local interface_status=$(ip link show "$vpn_interface" 2>/dev/null | grep "state UP")
        if [[ -n "$interface_status" ]]; then
            return 0
        fi
    fi
    
    return 1
}

# 显示连接信息
show_connection_info() {
    echo ""
    echo -e "${CYAN}连接信息:${NC}"
    
    # VPN接口信息
    local vpn_interface=$(ip link show | grep -E "tun[0-9]+" | head -1 | awk -F: '{print $2}' | tr -d ' ')
    if [[ -n "$vpn_interface" ]]; then
        local vpn_ip=$(ip addr show "$vpn_interface" | grep "inet " | awk '{print $2}' | head -1)
        echo "VPN接口: $vpn_interface ($vpn_ip)"
    fi
    
    # 外部IP
    local external_ip=$(curl -s --max-time 10 https://api.ipify.org 2>/dev/null || echo "无法获取")
    echo "外部IP: $external_ip"
    
    echo ""
}

# 查看连接状态
show_status() {
    echo -e "${CYAN}VPN连接状态${NC}"
    echo ""
    
    # 进程状态
    echo -e "${YELLOW}进程状态:${NC}"
    if pgrep -f openvpn >/dev/null; then
        echo -e "  OpenVPN进程: ${GREEN}运行中${NC}"
        local pid=$(pgrep -f openvpn | head -n1)
        echo "  进程ID: $pid"
    else
        echo -e "  OpenVPN进程: ${RED}未运行${NC}"
    fi
    
    echo ""
    
    # 网络接口
    echo -e "${YELLOW}网络接口:${NC}"
    local vpn_interfaces=$(ip link show | grep -E "tun[0-9]+|tap[0-9]+")
    if [[ -n "$vpn_interfaces" ]]; then
        echo "$vpn_interfaces" | while read line; do
            local interface=$(echo "$line" | awk -F: '{print $2}' | tr -d ' ')
            local status=$(echo "$line" | grep -o "state [A-Z]*" | awk '{print $2}')
            local ip=$(ip addr show "$interface" 2>/dev/null | grep "inet " | awk '{print $2}' | head -1)
            echo -e "  接口: $interface, 状态: $status, IP: $ip"
        done
    else
        echo -e "  ${RED}未找到VPN接口${NC}"
    fi
    
    echo ""
    
    # 路由信息
    echo -e "${YELLOW}路由信息:${NC}"
    local default_route=$(ip route show default | head -1)
    echo "  默认路由: $default_route"
    
    local vpn_routes=$(ip route show | grep -E "tun[0-9]+|tap[0-9]+" | head -3)
    if [[ -n "$vpn_routes" ]]; then
        echo "  VPN路由:"
        echo "$vpn_routes" | sed 's/^/    /'
    fi
}

# 测试网络连接
test_network() {
    echo -e "${CYAN}网络连接测试${NC}"
    echo ""

    # 测试目标
    local targets="google.com baidu.com qq.com youtube.com"

    for target in $targets; do
        echo -n "测试 $target: "
        if ping -c 3 -W 5 "$target" >/dev/null 2>&1; then
            local latency=$(ping -c 3 "$target" 2>/dev/null | tail -1 | awk -F '/' '{print $5}' | sed 's/[^0-9.]//g')
            if [[ -n "$latency" ]]; then
                echo -e "${GREEN}${latency}ms${NC}"
            else
                echo -e "${GREEN}连接正常${NC}"
            fi
        else
            echo -e "${RED}连接失败${NC}"
        fi
    done

    echo ""

    # 测试外部IP
    echo -n "外部IP地址: "
    local external_ip=$(curl -s --max-time 10 https://api.ipify.org 2>/dev/null)
    if [[ -n "$external_ip" ]]; then
        echo -e "${GREEN}$external_ip${NC}"
    else
        echo -e "${RED}无法获取${NC}"
    fi
}

# 查看日志
view_logs() {
    echo -e "${CYAN}查看VPN日志${NC}"
    echo ""

    local log_files=($(find "$LOG_DIR" -name "*.log" 2>/dev/null))

    if [[ ${#log_files[@]} -eq 0 ]]; then
        echo -e "${YELLOW}未找到日志文件${NC}"
        return
    fi

    echo "可用日志文件:"
    for i in "${!log_files[@]}"; do
        echo "$((i+1)). $(basename "${log_files[i]}")"
    done
    echo ""

    read -p "请选择日志文件编号: " choice

    if [[ "$choice" =~ ^[0-9]+$ ]] && [[ $choice -ge 1 && $choice -le ${#log_files[@]} ]]; then
        local log_file="${log_files[$((choice-1))]}"
        echo -e "${BLUE}显示日志: $(basename "$log_file")${NC}"
        echo ""
        tail -50 "$log_file"
    else
        echo -e "${RED}无效选择${NC}"
    fi
}

# DNS泄漏测试
dns_leak_test() {
    echo -e "${CYAN}DNS泄漏测试${NC}"
    echo ""

    if ! check_vpn_connected; then
        echo -e "${RED}VPN未连接，无法进行DNS泄漏测试${NC}"
        return 1
    fi

    echo "测试DNS服务器..."

    # 检查当前DNS配置
    echo -e "${YELLOW}当前DNS配置:${NC}"
    cat /etc/resolv.conf | grep nameserver
    echo ""

    # 测试DNS解析
    echo -e "${YELLOW}DNS解析测试:${NC}"
    local test_domains="google.com cloudflare.com"

    for domain in $test_domains; do
        echo -n "解析 $domain: "
        if dig +short "$domain" >/dev/null 2>&1; then
            local ip=$(dig +short "$domain" | head -1)
            echo -e "${GREEN}$ip${NC}"
        else
            echo -e "${RED}失败${NC}"
        fi
    done

    echo ""
    echo -e "${YELLOW}DNS泄漏检查:${NC}"
    echo "如果VPN正常工作，DNS查询应该通过VPN服务器"
    echo "建议访问 https://dnsleaktest.com 进行完整测试"
}

# 系统诊断
system_diagnosis() {
    echo -e "${CYAN}系统诊断${NC}"
    echo ""

    # OpenVPN版本
    echo -e "${YELLOW}OpenVPN版本:${NC}"
    openvpn --version | head -1
    echo ""

    # 系统信息
    echo -e "${YELLOW}系统信息:${NC}"
    echo "操作系统: $(uname -s) $(uname -r)"
    if [[ -f /etc/os-release ]]; then
        source /etc/os-release
        echo "发行版: $NAME $VERSION"
    fi
    echo ""

    # 容器环境检查
    if [[ "$IN_CONTAINER" == true ]]; then
        echo -e "${YELLOW}容器环境检查:${NC}"

        # TUN设备
        if [[ -c /dev/net/tun ]]; then
            echo -e "TUN设备: ${GREEN}可用${NC}"
            ls -la /dev/net/tun
        else
            echo -e "TUN设备: ${RED}不可用${NC}"
        fi

        # 网络权限
        if ip link add test-dummy type dummy 2>/dev/null; then
            ip link delete test-dummy 2>/dev/null
            echo -e "网络权限: ${GREEN}正常${NC}"
        else
            echo -e "网络权限: ${RED}受限${NC}"
        fi

        echo ""
    fi

    # 网络接口
    echo -e "${YELLOW}网络接口:${NC}"
    ip link show | head -10
    echo ""

    # 路由表
    echo -e "${YELLOW}路由表:${NC}"
    ip route show | head -10
    echo ""

    # 防火墙状态
    echo -e "${YELLOW}防火墙状态:${NC}"
    if iptables -L >/dev/null 2>&1; then
        local rule_count=$(iptables -L | wc -l)
        echo "iptables规则数量: $rule_count"
    else
        echo -e "${YELLOW}无法访问iptables${NC}"
    fi
}

# 主函数
main() {
    # 检查root权限
    if [[ $EUID -ne 0 ]]; then
        echo -e "${RED}错误: 需要root权限${NC}"
        echo -e "${YELLOW}请使用: sudo $0${NC}"
        exit 1
    fi
    
    # 检查OpenVPN是否安装
    if ! command -v openvpn >/dev/null 2>&1; then
        echo -e "${RED}错误: OpenVPN未安装${NC}"
        echo -e "${YELLOW}请先运行安装脚本${NC}"
        exit 1
    fi
    
    # 创建必要目录
    mkdir -p "$CONFIG_DIR" "$LOG_DIR"
    
    while true; do
        show_menu
        read -p "请选择操作 [0-8]: " choice
        echo ""
        
        case $choice in
            1) import_config ;;
            2) connect_vpn ;;
            3) disconnect_vpn ;;
            4) show_status ;;
            5) test_network ;;
            6) view_logs ;;
            7) dns_leak_test ;;
            8) system_diagnosis ;;
            0) echo -e "${GREEN}再见！${NC}"; exit 0 ;;
            *) echo -e "${RED}无效选择${NC}" ;;
        esac
        
        echo ""
        read -p "按回车键继续..."
        clear
    done
}

# 运行主程序
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
