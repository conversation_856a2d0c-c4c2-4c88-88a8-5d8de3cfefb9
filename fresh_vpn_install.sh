#!/bin/bash

# 全新VPN安装脚本 - OpenVPN 2.6.12 + 容器支持
# 专门为Docker容器环境和现代OpenVPN版本设计

set -euo pipefail

# 设置脚本权限
chmod +x "$0" 2>/dev/null || true

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 版本信息
OPENVPN_VERSION="2.6.12"
SCRIPT_VERSION="2.0.0"

echo -e "${BLUE}=== 全新VPN安装脚本 v${SCRIPT_VERSION} ===${NC}"
echo -e "${BLUE}目标OpenVPN版本: ${OPENVPN_VERSION}${NC}"
echo ""

# 检查root权限
if [[ $EUID -ne 0 ]]; then
    echo -e "${RED}错误: 需要root权限${NC}"
    echo -e "${YELLOW}请使用: sudo $0${NC}"
    exit 1
fi

# 检测环境
detect_environment() {
    echo -e "${CYAN}检测运行环境...${NC}"
    
    # 检测操作系统
    if [[ -f /etc/os-release ]]; then
        source /etc/os-release
        OS_ID=$ID
        OS_VERSION=$VERSION_ID
        echo "操作系统: $NAME $VERSION"
    else
        echo -e "${RED}无法检测操作系统${NC}"
        exit 1
    fi
    
    # 检测容器环境
    if [[ -f /.dockerenv ]] || grep -q docker /proc/1/cgroup 2>/dev/null; then
        IN_CONTAINER=true
        echo -e "${YELLOW}检测到Docker容器环境${NC}"
    else
        IN_CONTAINER=false
        echo "运行在标准Linux环境"
    fi
    
    echo ""
}

# 安装系统依赖
install_dependencies() {
    echo -e "${CYAN}安装系统依赖...${NC}"
    
    case $OS_ID in
        ubuntu|debian)
            apt-get update
            apt-get install -y \
                build-essential \
                libssl-dev \
                liblzo2-dev \
                libpam0g-dev \
                libpkcs11-helper1-dev \
                libsystemd-dev \
                pkg-config \
                autoconf \
                automake \
                libtool \
                wget \
                curl \
                ca-certificates \
                iptables \
                iproute2 \
                dnsutils \
                net-tools
            ;;
        centos|rhel|fedora)
            if command -v dnf >/dev/null 2>&1; then
                dnf install -y \
                    gcc gcc-c++ \
                    openssl-devel \
                    lzo-devel \
                    pam-devel \
                    pkcs11-helper-devel \
                    systemd-devel \
                    pkgconfig \
                    autoconf \
                    automake \
                    libtool \
                    wget \
                    curl \
                    ca-certificates \
                    iptables \
                    iproute \
                    bind-utils \
                    net-tools
            else
                yum install -y epel-release
                yum install -y \
                    gcc gcc-c++ \
                    openssl-devel \
                    lzo-devel \
                    pam-devel \
                    pkcs11-helper-devel \
                    systemd-devel \
                    pkgconfig \
                    autoconf \
                    automake \
                    libtool \
                    wget \
                    curl \
                    ca-certificates \
                    iptables \
                    iproute \
                    bind-utils \
                    net-tools
            fi
            ;;
        *)
            echo -e "${RED}不支持的操作系统: $OS_ID${NC}"
            exit 1
            ;;
    esac
    
    echo -e "${GREEN}✓ 系统依赖安装完成${NC}"
    echo ""
}

# 编译安装OpenVPN 2.6.12
install_openvpn() {
    echo -e "${CYAN}编译安装OpenVPN ${OPENVPN_VERSION}...${NC}"
    
    local build_dir="/tmp/openvpn-build"
    
    # 清理旧的构建目录
    rm -rf "$build_dir"
    mkdir -p "$build_dir"
    cd "$build_dir"
    
    # 下载源码
    echo "下载OpenVPN源码..."
    wget -q "https://swupdate.openvpn.org/community/releases/openvpn-${OPENVPN_VERSION}.tar.gz"
    
    if [[ ! -f "openvpn-${OPENVPN_VERSION}.tar.gz" ]]; then
        echo -e "${YELLOW}尝试备用下载地址...${NC}"
        wget -q "https://github.com/OpenVPN/openvpn/releases/download/v${OPENVPN_VERSION}/openvpn-${OPENVPN_VERSION}.tar.gz"
    fi
    
    if [[ ! -f "openvpn-${OPENVPN_VERSION}.tar.gz" ]]; then
        echo -e "${RED}无法下载OpenVPN源码${NC}"
        exit 1
    fi
    
    # 解压
    tar -xzf "openvpn-${OPENVPN_VERSION}.tar.gz"
    cd "openvpn-${OPENVPN_VERSION}"
    
    # 配置编译选项
    echo "配置编译选项..."
    ./configure \
        --enable-iproute2 \
        --enable-plugins \
        --enable-plugin-auth-pam \
        --enable-plugin-down-root \
        --enable-systemd \
        --prefix=/usr \
        --sysconfdir=/etc \
        --localstatedir=/var \
        --with-crypto-library=openssl
    
    # 编译
    echo "编译OpenVPN（这可能需要几分钟）..."
    make -j$(nproc) || make
    
    # 安装
    echo "安装OpenVPN..."
    make install
    
    # 创建必要目录
    mkdir -p /etc/openvpn/{client,server}
    mkdir -p /var/log/openvpn
    mkdir -p /etc/openvpn/keys
    
    # 清理构建目录
    cd /
    rm -rf "$build_dir"
    
    echo -e "${GREEN}✓ OpenVPN ${OPENVPN_VERSION} 安装完成${NC}"
    echo ""
}

# 容器环境特殊配置
setup_container_environment() {
    if [[ "$IN_CONTAINER" == true ]]; then
        echo -e "${CYAN}配置容器环境...${NC}"
        
        # 创建TUN设备
        if [[ ! -c /dev/net/tun ]]; then
            mkdir -p /dev/net
            mknod /dev/net/tun c 10 200 2>/dev/null || true
            chmod 666 /dev/net/tun 2>/dev/null || true
        fi
        
        if [[ -c /dev/net/tun ]]; then
            echo -e "${GREEN}✓ TUN设备已准备${NC}"
        else
            echo -e "${YELLOW}⚠ TUN设备创建失败，容器需要特权权限${NC}"
        fi
        
        # 检查网络权限
        if ip link add test-dummy type dummy 2>/dev/null; then
            ip link delete test-dummy 2>/dev/null
            echo -e "${GREEN}✓ 网络管理权限正常${NC}"
        else
            echo -e "${YELLOW}⚠ 缺少网络管理权限${NC}"
        fi
        
        echo ""
    fi
}

# 创建systemd服务
create_systemd_service() {
    echo -e "${CYAN}创建systemd服务...${NC}"
    
    cat > /etc/systemd/system/openvpn-client@.service << 'EOF'
[Unit]
Description=OpenVPN tunnel for %i
After=network-online.target
Wants=network-online.target
Documentation=man:openvpn(8)
Documentation=https://community.openvpn.net/openvpn/wiki/Openvpn24ManPage

[Service]
Type=notify
PrivateTmp=true
WorkingDirectory=/etc/openvpn/client
ExecStart=/usr/sbin/openvpn --suppress-timestamps --nobind --config %i.conf
CapabilityBoundingSet=CAP_IPC_LOCK CAP_NET_ADMIN CAP_NET_RAW CAP_SETGID CAP_SETUID CAP_SYS_CHROOT CAP_DAC_OVERRIDE
LimitNPROC=10
DeviceAllow=/dev/null rw
DeviceAllow=/dev/net/tun rw
ProtectSystem=true
ProtectHome=true
KillMode=process
RestartSec=5s
Restart=on-failure

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload || true
    echo -e "${GREEN}✓ systemd服务已创建${NC}"
    echo ""
}

# 创建配置目录结构
create_directory_structure() {
    echo -e "${CYAN}创建目录结构...${NC}"
    
    # 主目录
    mkdir -p /opt/vpn-client/{configs,logs,scripts,keys}
    
    # 配置目录
    mkdir -p /opt/vpn-client/configs/openvpn
    
    # 日志目录
    mkdir -p /opt/vpn-client/logs
    
    # 脚本目录
    mkdir -p /opt/vpn-client/scripts
    
    # 设置权限
    chmod 755 /opt/vpn-client
    chmod 700 /opt/vpn-client/keys
    
    echo -e "${GREEN}✓ 目录结构已创建${NC}"
    echo ""
}

# 验证安装
verify_installation() {
    echo -e "${CYAN}验证安装...${NC}"
    
    # 检查OpenVPN版本
    if command -v openvpn >/dev/null 2>&1; then
        local version=$(openvpn --version | head -1 | awk '{print $2}')
        echo "OpenVPN版本: $version"
        
        # 检查版本是否正确
        if [[ "$version" == "$OPENVPN_VERSION" ]]; then
            echo -e "${GREEN}✓ OpenVPN版本正确${NC}"
        else
            echo -e "${YELLOW}⚠ 版本不匹配，预期: $OPENVPN_VERSION，实际: $version${NC}"
        fi
        
        # 检查block-outside-dns支持
        if [[ "$version" =~ ^2\.[6-9]\. ]] || [[ "$version" =~ ^[3-9]\. ]]; then
            echo -e "${GREEN}✓ 支持block-outside-dns功能${NC}"
        else
            echo -e "${YELLOW}⚠ 可能不支持block-outside-dns${NC}"
        fi
    else
        echo -e "${RED}✗ OpenVPN未正确安装${NC}"
        return 1
    fi
    
    # 检查必要工具
    local tools=("ip" "iptables" "dig" "curl")
    for tool in "${tools[@]}"; do
        if command -v "$tool" >/dev/null 2>&1; then
            echo -e "${GREEN}✓ $tool 可用${NC}"
        else
            echo -e "${RED}✗ $tool 不可用${NC}"
        fi
    done
    
    echo ""
    return 0
}

# 主安装流程
main() {
    echo -e "${BLUE}开始全新VPN安装...${NC}"
    echo ""
    
    detect_environment
    install_dependencies
    install_openvpn
    setup_container_environment
    create_systemd_service
    create_directory_structure
    
    if verify_installation; then
        echo -e "${GREEN}=== 安装成功完成 ===${NC}"
        echo ""
        echo -e "${YELLOW}下一步:${NC}"
        echo "1. 将您的.ovpn配置文件复制到: /opt/vpn-client/configs/openvpn/"
        echo "2. 运行配置脚本进行VPN设置"
        echo "3. 测试VPN连接"
        echo ""
        
        if [[ "$IN_CONTAINER" == true ]]; then
            echo -e "${YELLOW}容器环境注意事项:${NC}"
            echo "- 确保容器以特权模式运行: --privileged"
            echo "- 或者添加权限: --cap-add=NET_ADMIN --device=/dev/net/tun"
            echo ""
        fi
        
        echo -e "${CYAN}安装信息:${NC}"
        echo "- OpenVPN版本: $(openvpn --version | head -1 | awk '{print $2}')"
        echo "- 配置目录: /opt/vpn-client/configs/openvpn/"
        echo "- 日志目录: /opt/vpn-client/logs/"
        echo "- 支持DNS泄漏防护: ✓"
        echo ""
    else
        echo -e "${RED}=== 安装失败 ===${NC}"
        echo "请检查错误信息并重试"
        exit 1
    fi
}

# 运行主程序
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
