# 全新VPN客户端 - OpenVPN 2.6.12

专为Docker容器环境和现代OpenVPN版本设计的VPN客户端，支持DNS泄漏防护。

## 🚀 快速开始

### 1. 删除旧文件夹并安装

```bash
# 删除旧的脚本文件夹
rm -rf proxy_server_augment/

# 下载新脚本（如果需要）
# 或者直接使用当前目录中的脚本

# 设置执行权限
chmod +x fresh_vpn_install.sh vpn_manager.sh

# 运行全新安装
sudo ./fresh_vpn_install.sh
```

### 2. 配置和连接VPN

```bash
# 运行VPN管理器
sudo ./vpn_manager.sh

# 在菜单中选择：
# 1. 导入配置文件 (导入您的 proxy-los-0812.ovpn)
# 2. 连接VPN
# 4. 查看连接状态
```

## 📋 功能特性

### ✅ 现代OpenVPN支持
- OpenVPN 2.6.12版本
- 完整支持 `block-outside-dns`
- DNS泄漏防护
- 现代加密算法

### ✅ 容器环境优化
- Docker容器完全支持
- 自动TUN设备创建
- 权限检查和修复
- 容器适配配置

### ✅ 安全功能
- DNS泄漏防护
- 强制路由重定向
- 安全DNS服务器 (*******, *******)
- 连接持久化

### ✅ 管理功能
- 图形化菜单界面
- 配置文件管理
- 连接状态监控
- 网络测试工具
- 系统诊断

## 🐳 Docker容器使用

### 推荐启动方式

```bash
# 特权模式（推荐）
docker run --privileged \
  -v /path/to/vpn/files:/opt/vpn-client/configs/openvpn \
  your-container

# 或者指定权限
docker run \
  --cap-add=NET_ADMIN \
  --device=/dev/net/tun \
  -v /path/to/vpn/files:/opt/vpn-client/configs/openvpn \
  your-container
```

### 容器内使用

```bash
# 1. 安装VPN客户端
sudo ./fresh_vpn_install.sh

# 2. 导入配置文件
sudo ./vpn_manager.sh
# 选择 1. 导入配置文件

# 3. 连接VPN
# 选择 2. 连接VPN
```

## 📁 目录结构

```
/opt/vpn-client/
├── configs/
│   └── openvpn/          # OpenVPN配置文件
├── logs/                 # 日志文件
├── scripts/              # 脚本文件
└── keys/                 # 密钥文件
```

## 🔧 配置说明

### 自动添加的安全配置

导入配置文件时，脚本会自动添加：

```
# DNS泄漏防护
ignore-unknown-option block-outside-dns
block-outside-dns
dhcp-option DNS *******
dhcp-option DNS *******

# 安全配置
script-security 2
redirect-gateway def1
route-delay 2
verb 3
persist-key
persist-tun
```

### 容器环境适配

在容器中会额外添加：

```
# 容器环境适配
dev tun
dev-type tun
topology subnet
user nobody
group nogroup
```

## 🧪 测试功能

### 网络连接测试
- 延迟测试 (Google, 百度, QQ, YouTube)
- 外部IP检查
- DNS解析测试

### DNS泄漏测试
- 当前DNS配置检查
- DNS解析功能测试
- 泄漏检测建议

### 系统诊断
- OpenVPN版本检查
- 容器环境检测
- 网络接口状态
- 路由表信息
- 防火墙状态

## 🔍 故障排除

### VPN无法连接

1. **检查容器权限**：
   ```bash
   # 检查TUN设备
   ls -la /dev/net/tun
   
   # 检查网络权限
   ip link add test-dummy type dummy
   ip link delete test-dummy
   ```

2. **查看日志**：
   ```bash
   # 在VPN管理器中选择 6. 查看日志
   # 或者直接查看
   tail -f /opt/vpn-client/logs/*.log
   ```

3. **系统诊断**：
   ```bash
   # 在VPN管理器中选择 8. 系统诊断
   ```

### DNS泄漏问题

1. **检查OpenVPN版本**：
   ```bash
   openvpn --version
   # 应该显示 2.6.12 或更高版本
   ```

2. **验证配置**：
   ```bash
   grep -i "block-outside-dns" /opt/vpn-client/configs/openvpn/*.ovpn
   ```

3. **DNS泄漏测试**：
   ```bash
   # 在VPN管理器中选择 7. DNS泄漏测试
   ```

## 📝 使用流程

1. **安装**: `sudo ./fresh_vpn_install.sh`
2. **管理**: `sudo ./vpn_manager.sh`
3. **导入**: 选择菜单项 1，导入您的 `.ovpn` 文件
4. **连接**: 选择菜单项 2，连接VPN
5. **测试**: 选择菜单项 5，测试网络连接
6. **验证**: 选择菜单项 7，进行DNS泄漏测试

## ⚠️ 注意事项

1. **权限要求**: 所有操作需要root权限
2. **容器环境**: 需要特权模式或NET_ADMIN权限
3. **网络隔离**: 某些容器网络模式可能影响VPN
4. **防火墙**: 确保防火墙不阻止VPN流量

## 🆘 支持

如果遇到问题，请提供：
1. 系统诊断输出 (菜单项 8)
2. VPN日志文件 (菜单项 6)
3. 容器启动命令
4. 错误信息截图

---

**版本**: 2.0.0  
**OpenVPN版本**: 2.6.12  
**支持环境**: Docker容器 + Linux
