#!/bin/bash

# Linux VPN客户端配置工具 - 专业级防IP泄漏版本
# 作者: VPN Security Assistant
# 版本: 2.0
# 描述: 专注于OpenVPN的安全VPN客户端，提供类似Tunnelblick的防IP泄漏保护

set -euo pipefail  # 严格模式

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MODULES_DIR="$SCRIPT_DIR/modules"
CONFIGS_DIR="$SCRIPT_DIR/configs"
LOGS_DIR="$SCRIPT_DIR/logs"
BACKUP_DIR="$SCRIPT_DIR/backup"

# 系统配置目录
SYSTEM_CONFIG_DIR="/etc/vpn-security-client"
USER_CONFIG_DIR="$HOME/.vpn-security-client"

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly WHITE='\033[1;37m'
readonly NC='\033[0m' # No Color

# 日志级别
readonly LOG_ERROR=1
readonly LOG_WARN=2
readonly LOG_INFO=3
readonly LOG_DEBUG=4

# 全局变量
CURRENT_LOG_LEVEL=${LOG_INFO}
VPN_STATUS="disconnected"
KILL_SWITCH_ACTIVE=false
GLOBAL_PROXY_MODE=false

# 创建必要目录
create_directories() {
    local dirs=("$MODULES_DIR" "$CONFIGS_DIR" "$LOGS_DIR" "$BACKUP_DIR" "$USER_CONFIG_DIR")
    for dir in "${dirs[@]}"; do
        mkdir -p "$dir" || {
            echo -e "${RED}错误: 无法创建目录 $dir${NC}" >&2
            exit 1
        }
    done

    # 创建系统配置目录 (需要sudo权限)
    if [[ $EUID -eq 0 ]]; then
        mkdir -p "$SYSTEM_CONFIG_DIR"
    fi
}

# 日志函数
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local log_file="$LOGS_DIR/vpn-client.log"

    if [[ $level -le $CURRENT_LOG_LEVEL ]]; then
        case $level in
            $LOG_ERROR) echo -e "${RED}[ERROR]${NC} $message" >&2 ;;
            $LOG_WARN)  echo -e "${YELLOW}[WARN]${NC} $message" ;;
            $LOG_INFO)  echo -e "${GREEN}[INFO]${NC} $message" ;;
            $LOG_DEBUG) echo -e "${BLUE}[DEBUG]${NC} $message" ;;
        esac
    fi

    echo "[$timestamp] [LEVEL:$level] $message" >> "$log_file"
}

# 显示标题
show_header() {
    clear
    echo -e "${CYAN}================================================================${NC}"
    echo -e "${WHITE}           Linux VPN安全客户端 v2.0${NC}"
    echo -e "${WHITE}           专业级防IP泄漏保护 (类似Tunnelblick)${NC}"
    echo -e "${CYAN}================================================================${NC}"
    echo ""

    # 显示当前状态
    echo -e "${BLUE}当前状态:${NC}"
    echo -e "  VPN连接: $(get_vpn_status_display)"
    echo -e "  Kill Switch: $(get_kill_switch_status_display)"
    echo -e "  代理模式: $(get_proxy_mode_display)"
    echo ""
}

# 获取VPN状态显示
get_vpn_status_display() {
    case $VPN_STATUS in
        "connected") echo -e "${GREEN}已连接${NC}" ;;
        "connecting") echo -e "${YELLOW}连接中${NC}" ;;
        "disconnected") echo -e "${RED}未连接${NC}" ;;
        *) echo -e "${RED}未知${NC}" ;;
    esac
}

# 获取Kill Switch状态显示
get_kill_switch_status_display() {
    if $KILL_SWITCH_ACTIVE; then
        echo -e "${GREEN}已激活${NC}"
    else
        echo -e "${RED}未激活${NC}"
    fi
}

# 获取代理模式显示
get_proxy_mode_display() {
    if $GLOBAL_PROXY_MODE; then
        echo -e "${RED}全局代理${NC}"
    else
        echo -e "${GREEN}智能代理${NC}"
    fi
}

# 显示主菜单
show_menu() {
    echo -e "${YELLOW}请选择操作:${NC}"
    echo ""
    echo -e "${GREEN}=== VPN连接管理 ===${NC}"
    echo -e "${GREEN}1.${NC} 配置OpenVPN连接"
    echo -e "${GREEN}2.${NC} 连接VPN"
    echo -e "${GREEN}3.${NC} 断开VPN"
    echo -e "${GREEN}4.${NC} 查看连接状态"
    echo ""
    echo -e "${BLUE}=== 安全防护 ===${NC}"
    echo -e "${BLUE}5.${NC} 启用Kill Switch (防断网泄漏)"
    echo -e "${BLUE}6.${NC} 禁用Kill Switch"
    echo -e "${BLUE}7.${NC} DNS泄漏检测"
    echo ""
    echo -e "${RED}=== 代理模式 ===${NC}"
    echo -e "${RED}8.${NC} 启用全局代理模式"
    echo -e "${RED}9.${NC} 恢复智能代理模式"
    echo ""
    echo -e "${PURPLE}=== 网络测试 ===${NC}"
    echo -e "${PURPLE}10.${NC} 延迟测试"
    echo -e "${PURPLE}11.${NC} IP地址检测"
    echo -e "${PURPLE}12.${NC} 完整安全检测"
    echo ""
    echo -e "${CYAN}=== 系统管理 ===${NC}"
    echo -e "${CYAN}13.${NC} 查看日志"
    echo -e "${CYAN}14.${NC} 备份/恢复配置"
    echo -e "${CYAN}15.${NC} 系统诊断"
    echo ""
    echo -e "${RED}0.${NC} 退出"
    echo ""
    echo -n -e "${YELLOW}请输入选项 [0-15]: ${NC}"
}

# 导入模块
load_modules() {
    local modules=("openvpn_core.sh" "security_core.sh" "network_test.sh")

    for module in "${modules[@]}"; do
        local module_path="$MODULES_DIR/$module"
        if [[ -f "$module_path" ]]; then
            source "$module_path" || {
                log_message $LOG_ERROR "无法加载模块: $module"
                echo -e "${RED}错误: 无法加载模块 $module${NC}" >&2
            }
        else
            log_message $LOG_WARN "模块文件不存在: $module_path"
        fi
    done
}

# 检查系统依赖
check_system_dependencies() {
    log_message $LOG_INFO "检查系统依赖"

    local missing_deps=()
    local required_commands=("ping" "ip" "iptables" "dig")
    local optional_commands=("curl" "wget" "openvpn")

    echo -e "${BLUE}检查系统依赖...${NC}"

    # 检查必需命令
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_deps+=("$cmd")
        fi
    done

    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        echo -e "${RED}错误: 缺少必需的系统命令: ${missing_deps[*]}${NC}"
        echo -e "${YELLOW}请安装缺少的软件包${NC}"
        return 1
    fi

    # 检查可选命令
    local missing_optional=()
    for cmd in "${optional_commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_optional+=("$cmd")
        fi
    done

    if [[ ${#missing_optional[@]} -gt 0 ]]; then
        echo -e "${YELLOW}警告: 缺少可选命令: ${missing_optional[*]}${NC}"
        echo -e "${YELLOW}某些功能可能受限${NC}"
    fi

    echo -e "${GREEN}系统依赖检查完成${NC}"
    return 0
}

# VPN连接管理
connect_vpn() {
    echo -e "${BLUE}连接VPN${NC}"
    echo ""

    # 检查是否已连接
    if pgrep -f openvpn >/dev/null; then
        echo -e "${YELLOW}VPN已经连接${NC}"
        read -p "是否要断开当前连接并重新连接? (y/N): " reconnect
        if [[ $reconnect =~ ^[Yy]$ ]]; then
            disconnect_vpn
        else
            return 0
        fi
    fi

    # 列出可用配置
    if ! list_openvpn_configs; then
        echo -e "${YELLOW}请先配置OpenVPN连接${NC}"
        return 1
    fi

    echo ""
    read -p "请选择配置编号 (回车使用默认配置): " config_choice

    local config_name
    if [[ -z "$config_choice" ]]; then
        # 使用默认配置
        if [[ -f "$USER_CONFIG_DIR/default_config" ]]; then
            config_name=$(cat "$USER_CONFIG_DIR/default_config")
        else
            echo -e "${RED}没有默认配置${NC}"
            return 1
        fi
    else
        # 根据选择获取配置名称
        local configs=($(ls "$OPENVPN_CONFIG_DIR"/*.ovpn 2>/dev/null | xargs -n1 basename | sed 's/\.ovpn$//' || true))
        if [[ $config_choice -gt 0 && $config_choice -le ${#configs[@]} ]]; then
            config_name="${configs[$((config_choice-1))]}"
        else
            echo -e "${RED}无效的选择${NC}"
            return 1
        fi
    fi

    local config_file="$OPENVPN_CONFIG_DIR/${config_name}.ovpn"

    if [[ ! -f "$config_file" ]]; then
        echo -e "${RED}配置文件不存在: $config_file${NC}"
        return 1
    fi

    echo -e "${BLUE}正在连接VPN: $config_name${NC}"
    log_message $LOG_INFO "开始连接VPN: $config_name"

    # 检查容器环境并准备TUN设备
    if [[ -f /.dockerenv ]] || grep -q docker /proc/1/cgroup 2>/dev/null; then
        log_message $LOG_INFO "检测到容器环境，准备TUN设备"

        # 确保TUN设备存在
        if [[ ! -c /dev/net/tun ]]; then
            mkdir -p /dev/net
            mknod /dev/net/tun c 10 200 2>/dev/null || true
            chmod 666 /dev/net/tun 2>/dev/null || true
        fi

        # 检查TUN设备权限
        if [[ ! -c /dev/net/tun ]]; then
            echo -e "${RED}错误: 无法创建TUN设备${NC}"
            echo -e "${YELLOW}容器需要特权模式或--cap-add=NET_ADMIN --device=/dev/net/tun${NC}"
            return 1
        fi
    fi

    # 启动OpenVPN
    local openvpn_cmd
    if [[ $EUID -eq 0 ]]; then
        openvpn_cmd="openvpn --config $config_file --daemon --writepid $OPENVPN_PID_FILE"
    else
        openvpn_cmd="sudo openvpn --config $config_file --daemon --writepid $OPENVPN_PID_FILE"
    fi

    # 在容器环境中添加额外参数
    if [[ -f /.dockerenv ]] || grep -q docker /proc/1/cgroup 2>/dev/null; then
        openvpn_cmd="$openvpn_cmd --script-security 2"
    fi

    eval $openvpn_cmd

    if [[ $? -eq 0 ]]; then
        VPN_STATUS="connecting"
        echo -e "${GREEN}VPN连接已启动${NC}"

        # 等待连接建立
        echo -e "${YELLOW}等待VPN连接建立...${NC}"
        local wait_count=0
        while [[ $wait_count -lt 30 ]]; do
            if check_vpn_connected; then
                VPN_STATUS="connected"
                echo -e "${GREEN}VPN连接成功！${NC}"
                log_message $LOG_INFO "VPN连接成功: $config_name"
                return 0
            fi
            sleep 1
            ((wait_count++))
            echo -n "."
        done

        echo ""
        echo -e "${YELLOW}VPN连接超时，请检查配置和网络${NC}"
        VPN_STATUS="disconnected"
        return 1
    else
        echo -e "${RED}VPN连接失败${NC}"
        VPN_STATUS="disconnected"
        return 1
    fi
}

# 断开VPN连接
disconnect_vpn() {
    echo -e "${BLUE}断开VPN连接${NC}"
    echo ""

    log_message $LOG_INFO "断开VPN连接"

    # 停止OpenVPN进程
    if [[ -f "$OPENVPN_PID_FILE" ]]; then
        local pid=$(cat "$OPENVPN_PID_FILE")
        if kill "$pid" 2>/dev/null; then
            echo -e "${GREEN}VPN进程已停止${NC}"
        fi
        rm -f "$OPENVPN_PID_FILE"
    else
        # 强制停止所有OpenVPN进程
        if [[ $EUID -eq 0 ]]; then
            pkill -f openvpn
        else
            sudo pkill -f openvpn
        fi
        echo -e "${YELLOW}已强制停止OpenVPN进程${NC}"
    fi

    VPN_STATUS="disconnected"
    echo -e "${GREEN}VPN已断开${NC}"
    log_message $LOG_INFO "VPN连接已断开"
}

# 检查VPN是否已连接
check_vpn_connected() {
    # 检查VPN接口是否存在且活跃
    local vpn_interface=$(ip link show | grep -E "tun[0-9]+|tap[0-9]+" | head -n1 | awk -F: '{print $2}' | tr -d ' ')

    if [[ -n "$vpn_interface" ]]; then
        local interface_status=$(ip link show "$vpn_interface" 2>/dev/null | grep "state UP")
        if [[ -n "$interface_status" ]]; then
            return 0
        fi
    fi

    return 1
}

# 查看VPN连接状态
show_vpn_status() {
    echo -e "${BLUE}VPN连接状态${NC}"
    echo ""

    echo -e "${YELLOW}进程状态:${NC}"
    if pgrep -f openvpn >/dev/null; then
        echo -e "  OpenVPN进程: ${GREEN}运行中${NC}"
        local pid=$(pgrep -f openvpn | head -n1)
        echo "  进程ID: $pid"
    else
        echo -e "  OpenVPN进程: ${RED}未运行${NC}"
    fi

    echo ""
    echo -e "${YELLOW}网络接口:${NC}"
    local vpn_interfaces=$(ip link show | grep -E "tun[0-9]+|tap[0-9]+")
    if [[ -n "$vpn_interfaces" ]]; then
        echo "$vpn_interfaces" | while read line; do
            local interface=$(echo "$line" | awk -F: '{print $2}' | tr -d ' ')
            local status=$(echo "$line" | grep -o "state [A-Z]*" | awk '{print $2}')
            echo -e "  接口: $interface, 状态: $status"
        done
    else
        echo -e "  ${RED}未找到VPN接口${NC}"
    fi

    echo ""
    echo -e "${YELLOW}路由信息:${NC}"
    ip route show | grep -E "tun[0-9]+|tap[0-9]+" | head -5

    if [[ -f "$OPENVPN_STATUS_FILE" ]]; then
        echo ""
        echo -e "${YELLOW}OpenVPN状态文件:${NC}"
        tail -10 "$OPENVPN_STATUS_FILE" 2>/dev/null || echo "无法读取状态文件"
    fi
}

# 启用全局代理模式
enable_global_proxy() {
    echo -e "${RED}启用全局代理模式${NC}"
    echo ""

    if $GLOBAL_PROXY_MODE; then
        echo -e "${YELLOW}全局代理模式已经启用${NC}"
        return 0
    fi

    echo -e "${YELLOW}警告: 全局代理模式将强制所有流量通过VPN${NC}"
    echo -e "${YELLOW}这将提供最高级别的IP保护，但可能影响某些本地服务${NC}"
    echo ""

    read -p "确认启用全局代理模式? (y/N): " confirm

    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}操作已取消${NC}"
        return 0
    fi

    # 检查VPN是否已连接
    if ! check_vpn_connected; then
        echo -e "${RED}错误: 请先连接VPN${NC}"
        return 1
    fi

    log_message $LOG_INFO "启用全局代理模式"

    # 备份当前路由表
    backup_routing_table

    # 设置全局代理路由
    if setup_global_proxy_routing; then
        GLOBAL_PROXY_MODE=true
        echo "GLOBAL_PROXY_MODE=true" > "$USER_CONFIG_DIR/proxy_mode.conf"
        echo -e "${GREEN}全局代理模式已启用${NC}"
        log_message $LOG_INFO "全局代理模式已启用"

        # 验证配置
        echo ""
        echo -e "${BLUE}验证全局代理配置...${NC}"
        verify_global_proxy_setup

        return 0
    else
        echo -e "${RED}全局代理模式启用失败${NC}"
        return 1
    fi
}

# 恢复智能代理模式
restore_smart_proxy() {
    echo -e "${GREEN}恢复智能代理模式${NC}"
    echo ""

    if ! $GLOBAL_PROXY_MODE; then
        echo -e "${YELLOW}当前已经是智能代理模式${NC}"
        return 0
    fi

    log_message $LOG_INFO "恢复智能代理模式"

    # 恢复路由表
    if restore_routing_table; then
        GLOBAL_PROXY_MODE=false
        echo "GLOBAL_PROXY_MODE=false" > "$USER_CONFIG_DIR/proxy_mode.conf"
        echo -e "${GREEN}智能代理模式已恢复${NC}"
        log_message $LOG_INFO "智能代理模式已恢复"
        return 0
    else
        echo -e "${RED}智能代理模式恢复失败${NC}"
        return 1
    fi
}

# 备份路由表
backup_routing_table() {
    log_message $LOG_INFO "备份当前路由表"

    ip route show > "$BACKUP_DIR/routes_backup.txt" 2>/dev/null || {
        log_message $LOG_WARN "无法备份路由表"
        return 1
    }

    # 备份默认网关
    ip route show default > "$BACKUP_DIR/default_gateway_backup.txt" 2>/dev/null

    return 0
}

# 恢复路由表
restore_routing_table() {
    log_message $LOG_INFO "恢复路由表"

    if [[ $EUID -ne 0 ]]; then
        echo -e "${RED}错误: 需要root权限来恢复路由表${NC}"
        return 1
    fi

    # 清除当前路由 (保留本地路由)
    ip route flush scope global 2>/dev/null || true

    # 恢复默认网关
    if [[ -f "$BACKUP_DIR/default_gateway_backup.txt" ]]; then
        while read route; do
            if [[ -n "$route" ]]; then
                ip route add $route 2>/dev/null || true
            fi
        done < "$BACKUP_DIR/default_gateway_backup.txt"
    fi

    return 0
}

# 设置全局代理路由
setup_global_proxy_routing() {
    log_message $LOG_INFO "设置全局代理路由"

    if [[ $EUID -ne 0 ]]; then
        echo -e "${RED}错误: 需要root权限来设置路由${NC}"
        return 1
    fi

    # 获取VPN接口和网关
    local vpn_interface=$(ip link show | grep -E "tun[0-9]+|tap[0-9]+" | head -n1 | awk -F: '{print $2}' | tr -d ' ')

    if [[ -z "$vpn_interface" ]]; then
        echo -e "${RED}错误: 未找到VPN接口${NC}"
        return 1
    fi

    # 获取VPN网关
    local vpn_gateway=$(ip route show dev "$vpn_interface" | grep -E "^0\.0\.0\.0|^default" | awk '{print $3}' | head -n1)

    if [[ -z "$vpn_gateway" ]]; then
        # 尝试从VPN接口获取点对点地址
        vpn_gateway=$(ip addr show "$vpn_interface" | grep "peer" | awk '{print $4}' | cut -d'/' -f1 | head -n1)
    fi

    if [[ -z "$vpn_gateway" ]]; then
        echo -e "${RED}错误: 无法确定VPN网关${NC}"
        return 1
    fi

    echo -e "${BLUE}VPN接口: $vpn_interface${NC}"
    echo -e "${BLUE}VPN网关: $vpn_gateway${NC}"

    # 删除现有的默认路由
    ip route del default 2>/dev/null || true

    # 添加VPN作为默认路由
    ip route add default via "$vpn_gateway" dev "$vpn_interface" metric 1

    # 确保VPN服务器可达 (通过原始网关)
    local vpn_server_ip=$(get_vpn_server_ip)
    if [[ -n "$vpn_server_ip" ]]; then
        local original_gateway=$(cat "$BACKUP_DIR/default_gateway_backup.txt" 2>/dev/null | awk '{print $3}' | head -n1)
        if [[ -n "$original_gateway" ]]; then
            ip route add "$vpn_server_ip" via "$original_gateway" 2>/dev/null || true
        fi
    fi

    # 刷新DNS缓存
    if command -v systemd-resolve >/dev/null 2>&1; then
        systemd-resolve --flush-caches 2>/dev/null || true
    elif command -v resolvectl >/dev/null 2>&1; then
        resolvectl flush-caches 2>/dev/null || true
    fi

    return 0
}

# 验证全局代理设置
verify_global_proxy_setup() {
    echo -e "${YELLOW}检查路由配置...${NC}"

    # 检查默认路由
    local default_route=$(ip route show default | head -n1)
    if echo "$default_route" | grep -E "tun[0-9]+|tap[0-9]+" >/dev/null; then
        echo -e "${GREEN}✓ 默认路由已设置为VPN${NC}"
    else
        echo -e "${RED}✗ 默认路由未正确设置${NC}"
        return 1
    fi

    # 检查DNS设置
    echo -e "${YELLOW}检查DNS配置...${NC}"
    if grep -q "*******\|*******" /etc/resolv.conf 2>/dev/null; then
        echo -e "${GREEN}✓ DNS已设置为安全DNS服务器${NC}"
    else
        echo -e "${YELLOW}⚠ 建议检查DNS配置${NC}"
    fi

    return 0
}

# 查看日志
show_logs() {
    echo -e "${BLUE}查看系统日志${NC}"
    echo ""

    echo "请选择要查看的日志:"
    echo "1. VPN客户端日志"
    echo "2. OpenVPN日志"
    echo "3. 系统安全日志"
    echo "4. 所有日志"

    read -p "请选择 [1-4]: " log_choice

    case $log_choice in
        1) show_client_logs ;;
        2) show_openvpn_logs ;;
        3) show_security_logs ;;
        4) show_all_logs ;;
        *) echo -e "${RED}无效选项${NC}" ;;
    esac
}

# 显示客户端日志
show_client_logs() {
    local log_file="$LOGS_DIR/vpn-client.log"

    if [[ -f "$log_file" ]]; then
        echo -e "${BLUE}VPN客户端日志 (最近50行):${NC}"
        echo ""
        tail -50 "$log_file"
    else
        echo -e "${YELLOW}客户端日志文件不存在${NC}"
    fi
}

# 显示OpenVPN日志
show_openvpn_logs() {
    if [[ -f "$OPENVPN_LOG_FILE" ]]; then
        echo -e "${BLUE}OpenVPN日志 (最近50行):${NC}"
        echo ""
        tail -50 "$OPENVPN_LOG_FILE"
    else
        echo -e "${YELLOW}OpenVPN日志文件不存在${NC}"
    fi
}

# 显示安全日志
show_security_logs() {
    echo -e "${BLUE}安全相关日志:${NC}"
    echo ""

    # 显示防火墙相关日志
    if command -v journalctl >/dev/null 2>&1; then
        echo -e "${YELLOW}防火墙日志 (最近20条):${NC}"
        journalctl -n 20 -u iptables 2>/dev/null || echo "无防火墙日志"
        echo ""
    fi

    # 显示网络相关日志
    echo -e "${YELLOW}网络接口日志:${NC}"
    dmesg | grep -E "tun|tap" | tail -10 2>/dev/null || echo "无网络接口日志"
}

# 显示所有日志
show_all_logs() {
    show_client_logs
    echo ""
    echo "================================"
    echo ""
    show_openvpn_logs
    echo ""
    echo "================================"
    echo ""
    show_security_logs
}

# 备份/恢复配置
backup_restore_config() {
    echo -e "${BLUE}配置备份/恢复${NC}"
    echo ""

    echo "请选择操作:"
    echo "1. 备份当前配置"
    echo "2. 恢复配置"
    echo "3. 列出备份文件"
    echo "4. 删除备份文件"

    read -p "请选择 [1-4]: " backup_choice

    case $backup_choice in
        1) create_config_backup ;;
        2) restore_config_backup ;;
        3) list_config_backups ;;
        4) delete_config_backup ;;
        *) echo -e "${RED}无效选项${NC}" ;;
    esac
}

# 创建配置备份
create_config_backup() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="$BACKUP_DIR/config_backup_$timestamp.tar.gz"

    echo -e "${BLUE}创建配置备份...${NC}"

    # 创建备份
    tar -czf "$backup_file" -C "$SCRIPT_DIR" configs logs 2>/dev/null
    tar -czf "${backup_file%.tar.gz}_user.tar.gz" -C "$USER_CONFIG_DIR" . 2>/dev/null

    if [[ $? -eq 0 ]]; then
        echo -e "${GREEN}配置备份成功: $backup_file${NC}"
        log_message $LOG_INFO "配置已备份: $backup_file"
    else
        echo -e "${RED}配置备份失败${NC}"
    fi
}

# 恢复配置备份
restore_config_backup() {
    echo -e "${BLUE}可用的备份文件:${NC}"

    local backups=($(ls "$BACKUP_DIR"/config_backup_*.tar.gz 2>/dev/null))

    if [[ ${#backups[@]} -eq 0 ]]; then
        echo -e "${YELLOW}没有找到备份文件${NC}"
        return 1
    fi

    for i in "${!backups[@]}"; do
        local backup_file="${backups[$i]}"
        local backup_name=$(basename "$backup_file")
        local backup_date=$(echo "$backup_name" | grep -o '[0-9]\{8\}_[0-9]\{6\}')
        echo "$((i+1)). $backup_name ($backup_date)"
    done

    echo ""
    read -p "请选择要恢复的备份编号: " backup_num

    if [[ $backup_num -gt 0 && $backup_num -le ${#backups[@]} ]]; then
        local selected_backup="${backups[$((backup_num-1))]}"

        echo -e "${YELLOW}警告: 这将覆盖当前配置${NC}"
        read -p "确认恢复备份? (y/N): " confirm

        if [[ $confirm =~ ^[Yy]$ ]]; then
            # 恢复备份
            tar -xzf "$selected_backup" -C "$SCRIPT_DIR" 2>/dev/null

            local user_backup="${selected_backup%.tar.gz}_user.tar.gz"
            if [[ -f "$user_backup" ]]; then
                tar -xzf "$user_backup" -C "$USER_CONFIG_DIR" 2>/dev/null
            fi

            echo -e "${GREEN}配置已恢复${NC}"
            log_message $LOG_INFO "配置已从备份恢复: $selected_backup"
        fi
    else
        echo -e "${RED}无效的选择${NC}"
    fi
}

# 列出配置备份
list_config_backups() {
    echo -e "${BLUE}配置备份列表:${NC}"
    echo ""

    local backups=($(ls -t "$BACKUP_DIR"/config_backup_*.tar.gz 2>/dev/null))

    if [[ ${#backups[@]} -eq 0 ]]; then
        echo -e "${YELLOW}没有找到备份文件${NC}"
        return 1
    fi

    for backup_file in "${backups[@]}"; do
        local backup_name=$(basename "$backup_file")
        local backup_size=$(du -h "$backup_file" | awk '{print $1}')
        local backup_date=$(stat -c %y "$backup_file" 2>/dev/null | cut -d' ' -f1,2 | cut -d'.' -f1)
        echo "  $backup_name ($backup_size, $backup_date)"
    done
}

# 删除配置备份
delete_config_backup() {
    echo -e "${BLUE}删除配置备份${NC}"
    echo ""

    list_config_backups
    echo ""

    read -p "请输入要删除的备份文件名 (不含路径): " backup_name

    local backup_file="$BACKUP_DIR/$backup_name"

    if [[ -f "$backup_file" ]]; then
        read -p "确认删除备份文件 $backup_name? (y/N): " confirm

        if [[ $confirm =~ ^[Yy]$ ]]; then
            rm -f "$backup_file"
            rm -f "${backup_file%.tar.gz}_user.tar.gz" 2>/dev/null
            echo -e "${GREEN}备份文件已删除${NC}"
            log_message $LOG_INFO "备份文件已删除: $backup_name"
        fi
    else
        echo -e "${RED}备份文件不存在${NC}"
    fi
}

# 系统诊断
system_diagnosis() {
    echo -e "${BLUE}系统诊断${NC}"
    echo ""

    log_message $LOG_INFO "开始系统诊断"

    echo -e "${CYAN}=== 1. 系统信息 ===${NC}"
    echo "操作系统: $(uname -s)"
    echo "内核版本: $(uname -r)"
    echo "架构: $(uname -m)"
    echo ""

    echo -e "${CYAN}=== 2. 网络配置 ===${NC}"
    echo -e "${YELLOW}网络接口:${NC}"
    ip addr show | grep -E "^[0-9]+:|inet " | head -20
    echo ""

    echo -e "${YELLOW}路由表:${NC}"
    ip route show | head -10
    echo ""

    echo -e "${YELLOW}DNS配置:${NC}"
    cat /etc/resolv.conf 2>/dev/null | head -10
    echo ""

    echo -e "${CYAN}=== 3. VPN状态 ===${NC}"
    if pgrep -f openvpn >/dev/null; then
        echo -e "${GREEN}✓ OpenVPN进程运行中${NC}"

        local vpn_interface=$(ip link show | grep -E "tun[0-9]+|tap[0-9]+" | head -n1)
        if [[ -n "$vpn_interface" ]]; then
            echo -e "${GREEN}✓ VPN接口已创建${NC}"
            echo "$vpn_interface"
        else
            echo -e "${RED}✗ 未找到VPN接口${NC}"
        fi
    else
        echo -e "${RED}✗ OpenVPN进程未运行${NC}"
    fi
    echo ""

    echo -e "${CYAN}=== 4. 安全状态 ===${NC}"
    if $KILL_SWITCH_ACTIVE; then
        echo -e "${GREEN}✓ Kill Switch已激活${NC}"
    else
        echo -e "${YELLOW}⚠ Kill Switch未激活${NC}"
    fi

    if $GLOBAL_PROXY_MODE; then
        echo -e "${GREEN}✓ 全局代理模式已启用${NC}"
    else
        echo -e "${BLUE}ℹ 智能代理模式${NC}"
    fi
    echo ""

    echo -e "${CYAN}=== 5. 防火墙状态 ===${NC}"
    if command -v iptables >/dev/null 2>&1; then
        local iptables_rules=$(iptables -L | wc -l)
        echo "iptables规则数量: $iptables_rules"

        if [[ $iptables_rules -gt 10 ]]; then
            echo -e "${GREEN}✓ 防火墙规则已配置${NC}"
        else
            echo -e "${YELLOW}⚠ 防火墙规则较少${NC}"
        fi
    else
        echo -e "${RED}✗ iptables不可用${NC}"
    fi
    echo ""

    echo -e "${CYAN}=== 6. 依赖检查 ===${NC}"
    local required_commands=("ping" "ip" "iptables" "dig" "openvpn")
    for cmd in "${required_commands[@]}"; do
        if command -v "$cmd" >/dev/null 2>&1; then
            echo -e "${GREEN}✓ $cmd${NC}"
        else
            echo -e "${RED}✗ $cmd (缺失)${NC}"
        fi
    done
    echo ""

    echo -e "${GREEN}=== 系统诊断完成 ===${NC}"
    log_message $LOG_INFO "系统诊断完成"
}

# 初始化系统
initialize_system() {
    log_message $LOG_INFO "初始化VPN安全客户端"

    # 创建必要目录
    create_directories

    # 检查系统依赖
    if ! check_system_dependencies; then
        echo -e "${RED}系统依赖检查失败，某些功能可能无法正常工作${NC}"
    fi

    # 加载模块
    load_modules

    # 初始化安全模块
    init_security_module

    # 读取保存的状态
    if [[ -f "$USER_CONFIG_DIR/proxy_mode.conf" ]]; then
        source "$USER_CONFIG_DIR/proxy_mode.conf"
    fi

    log_message $LOG_INFO "系统初始化完成"
}

# 清理退出
cleanup_and_exit() {
    echo ""
    echo -e "${BLUE}正在清理并退出...${NC}"

    log_message $LOG_INFO "程序退出"

    # 如果Kill Switch激活，询问是否保持
    if $KILL_SWITCH_ACTIVE; then
        echo -e "${YELLOW}Kill Switch当前处于激活状态${NC}"
        read -p "退出时是否保持Kill Switch激活? (Y/n): " keep_killswitch

        if [[ $keep_killswitch =~ ^[Nn]$ ]]; then
            echo -e "${BLUE}禁用Kill Switch...${NC}"
            disable_kill_switch
        fi
    fi

    echo -e "${GREEN}感谢使用Linux VPN安全客户端！${NC}"
    exit 0
}

# 主程序循环
main() {
    # 捕获退出信号
    trap cleanup_and_exit SIGINT SIGTERM

    # 初始化系统
    initialize_system

    while true; do
        show_header
        show_menu

        read choice
        echo ""

        case $choice in
            1) configure_openvpn ;;
            2) connect_vpn ;;
            3) disconnect_vpn ;;
            4) show_vpn_status ;;
            5) enable_kill_switch ;;
            6) disable_kill_switch ;;
            7) check_dns_leak ;;
            8) enable_global_proxy ;;
            9) restore_smart_proxy ;;
            10) test_network_latency ;;
            11) check_ip_address ;;
            12) comprehensive_security_check ;;
            13) show_logs ;;
            14) backup_restore_config ;;
            15) system_diagnosis ;;
            0) cleanup_and_exit ;;
            *)
                echo -e "${RED}无效选项，请重新选择${NC}"
                ;;
        esac

        echo ""
        read -p "按回车键继续..."
    done
}

# 检查是否以脚本方式运行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

# 配置Shadowsocks
configure_shadowsocks() {
    echo -e "${BLUE}配置Shadowsocks代理${NC}"
    echo ""
    
    read -p "请输入服务器地址: " ss_server
    read -p "请输入端口: " ss_port
    read -p "请输入密码: " ss_password
    read -p "请输入加密方法 (如: aes-256-gcm): " ss_method
    
    # 创建Shadowsocks配置文件
    cat > "$CONFIG_DIR/shadowsocks.json" << EOF
{
    "server": "$ss_server",
    "server_port": $ss_port,
    "password": "$ss_password",
    "method": "$ss_method",
    "local_address": "127.0.0.1",
    "local_port": 1080,
    "timeout": 300
}
EOF
    
    echo -e "${GREEN}Shadowsocks配置已保存${NC}"
    log_message "Shadowsocks配置已更新: $ss_server:$ss_port"
}

# 配置V2Ray
configure_v2ray() {
    echo -e "${BLUE}配置V2Ray代理${NC}"
    echo ""
    
    echo "请选择配置方式:"
    echo "1. 手动输入配置"
    echo "2. 导入vmess://链接"
    echo "3. 导入vless://链接"
    read -p "请选择 [1-3]: " v2ray_type
    
    case $v2ray_type in
        1)
            configure_v2ray_manual
            ;;
        2)
            configure_v2ray_vmess
            ;;
        3)
            configure_v2ray_vless
            ;;
        *)
            echo -e "${RED}无效选项${NC}"
            return
            ;;
    esac
}

# 手动配置V2Ray
configure_v2ray_manual() {
    read -p "请输入服务器地址: " v2_server
    read -p "请输入端口: " v2_port
    read -p "请输入UUID: " v2_uuid
    read -p "请输入alterId (默认0): " v2_alterid
    v2_alterid=${v2_alterid:-0}
    read -p "请输入网络类型 (tcp/ws/h2): " v2_network
    read -p "请输入路径 (WebSocket路径，可选): " v2_path
    
    # 创建V2Ray配置文件
    cat > "$CONFIG_DIR/v2ray.json" << EOF
{
    "inbounds": [{
        "port": 1080,
        "protocol": "socks",
        "settings": {
            "auth": "noauth"
        }
    }],
    "outbounds": [{
        "protocol": "vmess",
        "settings": {
            "vnext": [{
                "address": "$v2_server",
                "port": $v2_port,
                "users": [{
                    "id": "$v2_uuid",
                    "alterId": $v2_alterid
                }]
            }]
        },
        "streamSettings": {
            "network": "$v2_network"
            $(if [ "$v2_network" = "ws" ] && [ -n "$v2_path" ]; then
                echo ",\"wsSettings\": {\"path\": \"$v2_path\"}"
            fi)
        }
    }]
}
EOF
    
    echo -e "${GREEN}V2Ray配置已保存${NC}"
    log_message "V2Ray配置已更新: $v2_server:$v2_port"
}

# 通过vmess链接配置V2Ray
configure_v2ray_vmess() {
    read -p "请输入vmess://链接: " vmess_link
    
    # 解析vmess链接 (简化版本，实际需要更复杂的解析)
    if [[ $vmess_link =~ ^vmess:// ]]; then
        echo -e "${GREEN}正在解析vmess链接...${NC}"
        # 这里需要实现vmess链接解析逻辑
        echo -e "${YELLOW}vmess链接解析功能需要进一步实现${NC}"
        log_message "尝试配置vmess链接: $vmess_link"
    else
        echo -e "${RED}无效的vmess链接格式${NC}"
    fi
}

# 通过vless链接配置V2Ray
configure_v2ray_vless() {
    read -p "请输入vless://链接: " vless_link
    
    if [[ $vless_link =~ ^vless:// ]]; then
        echo -e "${GREEN}正在解析vless链接...${NC}"
        # 这里需要实现vless链接解析逻辑
        echo -e "${YELLOW}vless链接解析功能需要进一步实现${NC}"
        log_message "尝试配置vless链接: $vless_link"
    else
        echo -e "${RED}无效的vless链接格式${NC}"
    fi
}

# 配置Trojan
configure_trojan() {
    echo -e "${BLUE}配置Trojan代理${NC}"
    echo ""
    
    read -p "请输入服务器地址: " trojan_server
    read -p "请输入端口: " trojan_port
    read -p "请输入密码: " trojan_password
    read -p "请输入SNI (可选): " trojan_sni
    
    # 创建Trojan配置文件
    cat > "$CONFIG_DIR/trojan.json" << EOF
{
    "run_type": "client",
    "local_addr": "127.0.0.1",
    "local_port": 1080,
    "remote_addr": "$trojan_server",
    "remote_port": $trojan_port,
    "password": ["$trojan_password"],
    "ssl": {
        "verify": true,
        "verify_hostname": true,
        "cert": "",
        "cipher": "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256",
        "sni": "$trojan_sni"
    }
}
EOF
    
    echo -e "${GREEN}Trojan配置已保存${NC}"
    log_message "Trojan配置已更新: $trojan_server:$trojan_port"
}

# 配置OpenVPN
configure_openvpn() {
    echo -e "${BLUE}配置OpenVPN${NC}"
    echo ""
    
    read -p "请输入.ovpn配置文件路径: " ovpn_file
    
    if [ -f "$ovpn_file" ]; then
        cp "$ovpn_file" "$CONFIG_DIR/client.ovpn"
        echo -e "${GREEN}OpenVPN配置文件已复制${NC}"
        log_message "OpenVPN配置已更新: $ovpn_file"
    else
        echo -e "${RED}配置文件不存在: $ovpn_file${NC}"
    fi
}

# 配置WireGuard
configure_wireguard() {
    echo -e "${BLUE}配置WireGuard${NC}"
    echo ""

    read -p "请输入WireGuard配置文件路径: " wg_file

    if [ -f "$wg_file" ]; then
        cp "$wg_file" "$CONFIG_DIR/wg0.conf"
        echo -e "${GREEN}WireGuard配置文件已复制${NC}"
        log_message "WireGuard配置已更新: $wg_file"
    else
        echo -e "${RED}配置文件不存在: $wg_file${NC}"
    fi
}

# 测试网络延迟
test_network_latency() {
    # 检查是否有网络测试模块，如果有就使用模块
    if [[ -f "$MODULES_DIR/network_test.sh" ]]; then
        source "$MODULES_DIR/network_test.sh"
        test_network_latency
        return
    fi

    # 如果没有模块，使用简化版本
    echo -e "${BLUE}测试网络延迟${NC}"
    echo ""

    # 测试目标列表 - 使用简单格式避免关联数组问题
    local test_targets="Google:google.com QQ:qq.com Baidu:baidu.com Alibaba:alibaba.com YouTube:youtube.com GoogleSearch:www.google.com"

    echo -e "${YELLOW}开始延迟测试...${NC}"
    echo ""

    for target_pair in $test_targets; do
        local name=$(echo "$target_pair" | cut -d: -f1)
        local target=$(echo "$target_pair" | cut -d: -f2)
        echo -n "测试 $name ($target): "

        # 使用ping测试延迟
        if command -v ping >/dev/null 2>&1; then
            local ping_result=$(ping -c 3 -W 3 "$target" 2>/dev/null)
            if [[ $? -eq 0 && -n "$ping_result" ]]; then
                # 提取平均延迟
                local latency=$(echo "$ping_result" | grep "min/avg/max" | awk -F '/' '{print $5}' | sed 's/[^0-9.]//g')
                if [[ -z "$latency" ]]; then
                    # 尝试另一种解析方式
                    latency=$(echo "$ping_result" | tail -1 | awk '{print $4}' | cut -d'/' -f2)
                fi

                if [[ -n "$latency" && "$latency" =~ ^[0-9]+\.?[0-9]*$ ]]; then
                    echo -e "${GREEN}${latency}ms${NC}"
                    log_message $LOG_INFO "延迟测试 $name: ${latency}ms"
                else
                    echo -e "${YELLOW}无法解析延迟数据${NC}"
                    log_message $LOG_WARN "延迟测试 $name: 无法解析延迟数据"
                fi
            else
                echo -e "${RED}连接失败${NC}"
                log_message $LOG_WARN "延迟测试 $name: 连接失败"
            fi
        else
            echo -e "${RED}ping命令不可用${NC}"
        fi
    done

    echo ""
    echo -e "${CYAN}延迟测试完成${NC}"
}

# 查看当前配置状态
show_current_status() {
    echo -e "${BLUE}当前配置状态${NC}"
    echo ""

    # 检查各种配置文件是否存在
    echo "配置文件状态:"

    if [ -f "$CONFIG_DIR/shadowsocks.json" ]; then
        echo -e "  Shadowsocks: ${GREEN}已配置${NC}"
    else
        echo -e "  Shadowsocks: ${RED}未配置${NC}"
    fi

    if [ -f "$CONFIG_DIR/v2ray.json" ]; then
        echo -e "  V2Ray: ${GREEN}已配置${NC}"
    else
        echo -e "  V2Ray: ${RED}未配置${NC}"
    fi

    if [ -f "$CONFIG_DIR/trojan.json" ]; then
        echo -e "  Trojan: ${GREEN}已配置${NC}"
    else
        echo -e "  Trojan: ${RED}未配置${NC}"
    fi

    if [ -f "$CONFIG_DIR/client.ovpn" ]; then
        echo -e "  OpenVPN: ${GREEN}已配置${NC}"
    else
        echo -e "  OpenVPN: ${RED}未配置${NC}"
    fi

    if [ -f "$CONFIG_DIR/wg0.conf" ]; then
        echo -e "  WireGuard: ${GREEN}已配置${NC}"
    else
        echo -e "  WireGuard: ${RED}未配置${NC}"
    fi

    echo ""

    # 检查代理状态
    echo "代理状态:"
    if [ -f "$CONFIG_DIR/global_proxy_enabled" ]; then
        echo -e "  模式: ${RED}全局代理模式${NC}"
    else
        echo -e "  模式: ${GREEN}智能代理模式${NC}"
    fi

    # 检查系统代理设置
    if command -v gsettings >/dev/null 2>&1; then
        proxy_mode=$(gsettings get org.gnome.system.proxy mode 2>/dev/null || echo "none")
        echo "  系统代理: $proxy_mode"
    fi
}

# 设置全局代理模式
set_global_proxy() {
    echo -e "${RED}设置全局代理模式${NC}"
    echo ""
    echo -e "${YELLOW}警告: 全局代理模式将使所有流量通过代理服务器${NC}"
    echo -e "${YELLOW}这可能会影响网络性能，但能更好地保护您的IP地址${NC}"
    echo ""

    read -p "确认启用全局代理模式? (y/N): " confirm

    if [[ $confirm =~ ^[Yy]$ ]]; then
        # 备份当前网络配置
        backup_network_config

        # 设置全局代理标记
        touch "$CONFIG_DIR/global_proxy_enabled"

        # 配置iptables规则 (需要root权限)
        echo -e "${BLUE}配置iptables规则...${NC}"

        # 检查是否有root权限
        if [ "$EUID" -eq 0 ]; then
            setup_global_proxy_rules
        else
            echo -e "${YELLOW}需要root权限来配置iptables规则${NC}"
            echo "请运行: sudo $0"
            echo "或手动执行以下命令:"
            echo ""
            show_global_proxy_commands
        fi

        log_message "全局代理模式已启用"
        echo -e "${GREEN}全局代理模式已启用${NC}"
    else
        echo -e "${YELLOW}操作已取消${NC}"
    fi
}

# 恢复智能代理模式
restore_smart_proxy() {
    echo -e "${GREEN}恢复智能代理模式${NC}"
    echo ""

    if [ -f "$CONFIG_DIR/global_proxy_enabled" ]; then
        echo -e "${BLUE}正在恢复网络配置...${NC}"

        # 移除全局代理标记
        rm -f "$CONFIG_DIR/global_proxy_enabled"

        # 恢复网络配置
        restore_network_config

        # 清除iptables规则
        if [ "$EUID" -eq 0 ]; then
            clear_global_proxy_rules
        else
            echo -e "${YELLOW}需要root权限来清除iptables规则${NC}"
            echo "请运行: sudo $0"
            echo "或手动执行以下命令:"
            echo ""
            show_clear_proxy_commands
        fi

        log_message "智能代理模式已恢复"
        echo -e "${GREEN}智能代理模式已恢复${NC}"
    else
        echo -e "${YELLOW}当前已经是智能代理模式${NC}"
    fi
}

# 备份网络配置
backup_network_config() {
    echo -e "${BLUE}备份当前网络配置...${NC}"

    # 备份路由表
    ip route show > "$BACKUP_DIR/routes.backup" 2>/dev/null

    # 备份DNS配置
    cp /etc/resolv.conf "$BACKUP_DIR/resolv.conf.backup" 2>/dev/null

    # 备份iptables规则
    if command -v iptables-save >/dev/null 2>&1; then
        iptables-save > "$BACKUP_DIR/iptables.backup" 2>/dev/null
    fi

    log_message "网络配置已备份"
}

# 恢复网络配置
restore_network_config() {
    echo -e "${BLUE}恢复网络配置...${NC}"

    # 恢复iptables规则
    if [ -f "$BACKUP_DIR/iptables.backup" ] && command -v iptables-restore >/dev/null 2>&1; then
        iptables-restore < "$BACKUP_DIR/iptables.backup" 2>/dev/null
    fi

    # 恢复DNS配置
    if [ -f "$BACKUP_DIR/resolv.conf.backup" ]; then
        cp "$BACKUP_DIR/resolv.conf.backup" /etc/resolv.conf 2>/dev/null
    fi

    log_message "网络配置已恢复"
}

# 设置全局代理规则
setup_global_proxy_rules() {
    echo -e "${BLUE}设置全局代理iptables规则...${NC}"

    # 创建新的链
    iptables -t nat -N PROXY_CHAIN 2>/dev/null

    # 跳过本地地址
    iptables -t nat -A PROXY_CHAIN -d *********/8 -j RETURN
    iptables -t nat -A PROXY_CHAIN -d ***********/16 -j RETURN
    iptables -t nat -A PROXY_CHAIN -d 10.0.0.0/8 -j RETURN
    iptables -t nat -A PROXY_CHAIN -d **********/12 -j RETURN

    # 重定向TCP流量到代理端口
    iptables -t nat -A PROXY_CHAIN -p tcp -j REDIRECT --to-ports 1080

    # 应用规则到OUTPUT链
    iptables -t nat -A OUTPUT -p tcp -j PROXY_CHAIN

    echo -e "${GREEN}全局代理规则已设置${NC}"
}

# 清除全局代理规则
clear_global_proxy_rules() {
    echo -e "${BLUE}清除全局代理iptables规则...${NC}"

    # 删除OUTPUT链中的规则
    iptables -t nat -D OUTPUT -p tcp -j PROXY_CHAIN 2>/dev/null

    # 清空并删除自定义链
    iptables -t nat -F PROXY_CHAIN 2>/dev/null
    iptables -t nat -X PROXY_CHAIN 2>/dev/null

    echo -e "${GREEN}全局代理规则已清除${NC}"
}

# 显示全局代理命令
show_global_proxy_commands() {
    echo "# 设置全局代理规则"
    echo "iptables -t nat -N PROXY_CHAIN"
    echo "iptables -t nat -A PROXY_CHAIN -d *********/8 -j RETURN"
    echo "iptables -t nat -A PROXY_CHAIN -d ***********/16 -j RETURN"
    echo "iptables -t nat -A PROXY_CHAIN -d 10.0.0.0/8 -j RETURN"
    echo "iptables -t nat -A PROXY_CHAIN -d **********/12 -j RETURN"
    echo "iptables -t nat -A PROXY_CHAIN -p tcp -j REDIRECT --to-ports 1080"
    echo "iptables -t nat -A OUTPUT -p tcp -j PROXY_CHAIN"
}

# 显示清除代理命令
show_clear_proxy_commands() {
    echo "# 清除全局代理规则"
    echo "iptables -t nat -D OUTPUT -p tcp -j PROXY_CHAIN"
    echo "iptables -t nat -F PROXY_CHAIN"
    echo "iptables -t nat -X PROXY_CHAIN"
}

# 启动/停止代理服务
manage_proxy_service() {
    echo -e "${BLUE}代理服务管理${NC}"
    echo ""

    echo "请选择操作:"
    echo "1. 启动Shadowsocks"
    echo "2. 停止Shadowsocks"
    echo "3. 启动V2Ray"
    echo "4. 停止V2Ray"
    echo "5. 启动Trojan"
    echo "6. 停止Trojan"
    echo "7. 启动OpenVPN"
    echo "8. 停止OpenVPN"
    echo "9. 启动WireGuard"
    echo "10. 停止WireGuard"
    echo "11. 查看服务状态"

    read -p "请选择 [1-11]: " service_choice

    case $service_choice in
        1) start_shadowsocks ;;
        2) stop_shadowsocks ;;
        3) start_v2ray ;;
        4) stop_v2ray ;;
        5) start_trojan ;;
        6) stop_trojan ;;
        7) start_openvpn ;;
        8) stop_openvpn ;;
        9) start_wireguard ;;
        10) stop_wireguard ;;
        11) show_service_status ;;
        *) echo -e "${RED}无效选项${NC}" ;;
    esac
}

# 启动Shadowsocks
start_shadowsocks() {
    if [ -f "$CONFIG_DIR/shadowsocks.json" ]; then
        echo -e "${BLUE}启动Shadowsocks...${NC}"
        if command -v ss-local >/dev/null 2>&1; then
            ss-local -c "$CONFIG_DIR/shadowsocks.json" -f "$CONFIG_DIR/ss.pid" &
            echo -e "${GREEN}Shadowsocks已启动${NC}"
            log_message "Shadowsocks服务已启动"
        else
            echo -e "${RED}ss-local命令不存在，请安装shadowsocks-libev${NC}"
        fi
    else
        echo -e "${RED}Shadowsocks配置文件不存在${NC}"
    fi
}

# 停止Shadowsocks
stop_shadowsocks() {
    echo -e "${BLUE}停止Shadowsocks...${NC}"
    if [ -f "$CONFIG_DIR/ss.pid" ]; then
        kill $(cat "$CONFIG_DIR/ss.pid") 2>/dev/null
        rm -f "$CONFIG_DIR/ss.pid"
        echo -e "${GREEN}Shadowsocks已停止${NC}"
        log_message "Shadowsocks服务已停止"
    else
        pkill -f ss-local 2>/dev/null
        echo -e "${YELLOW}尝试强制停止Shadowsocks进程${NC}"
    fi
}

# 启动V2Ray
start_v2ray() {
    if [ -f "$CONFIG_DIR/v2ray.json" ]; then
        echo -e "${BLUE}启动V2Ray...${NC}"
        if command -v v2ray >/dev/null 2>&1; then
            v2ray -config "$CONFIG_DIR/v2ray.json" &
            echo $! > "$CONFIG_DIR/v2ray.pid"
            echo -e "${GREEN}V2Ray已启动${NC}"
            log_message "V2Ray服务已启动"
        else
            echo -e "${RED}v2ray命令不存在，请安装V2Ray${NC}"
        fi
    else
        echo -e "${RED}V2Ray配置文件不存在${NC}"
    fi
}

# 停止V2Ray
stop_v2ray() {
    echo -e "${BLUE}停止V2Ray...${NC}"
    if [ -f "$CONFIG_DIR/v2ray.pid" ]; then
        kill $(cat "$CONFIG_DIR/v2ray.pid") 2>/dev/null
        rm -f "$CONFIG_DIR/v2ray.pid"
        echo -e "${GREEN}V2Ray已停止${NC}"
        log_message "V2Ray服务已停止"
    else
        pkill -f v2ray 2>/dev/null
        echo -e "${YELLOW}尝试强制停止V2Ray进程${NC}"
    fi
}

# 启动Trojan
start_trojan() {
    if [ -f "$CONFIG_DIR/trojan.json" ]; then
        echo -e "${BLUE}启动Trojan...${NC}"
        if command -v trojan >/dev/null 2>&1; then
            trojan -c "$CONFIG_DIR/trojan.json" &
            echo $! > "$CONFIG_DIR/trojan.pid"
            echo -e "${GREEN}Trojan已启动${NC}"
            log_message "Trojan服务已启动"
        else
            echo -e "${RED}trojan命令不存在，请安装Trojan${NC}"
        fi
    else
        echo -e "${RED}Trojan配置文件不存在${NC}"
    fi
}

# 停止Trojan
stop_trojan() {
    echo -e "${BLUE}停止Trojan...${NC}"
    if [ -f "$CONFIG_DIR/trojan.pid" ]; then
        kill $(cat "$CONFIG_DIR/trojan.pid") 2>/dev/null
        rm -f "$CONFIG_DIR/trojan.pid"
        echo -e "${GREEN}Trojan已停止${NC}"
        log_message "Trojan服务已停止"
    else
        pkill -f trojan 2>/dev/null
        echo -e "${YELLOW}尝试强制停止Trojan进程${NC}"
    fi
}

# 启动OpenVPN
start_openvpn() {
    if [ -f "$CONFIG_DIR/client.ovpn" ]; then
        echo -e "${BLUE}启动OpenVPN...${NC}"
        if command -v openvpn >/dev/null 2>&1; then
            sudo openvpn --config "$CONFIG_DIR/client.ovpn" --daemon --writepid "$CONFIG_DIR/openvpn.pid"
            echo -e "${GREEN}OpenVPN已启动${NC}"
            log_message "OpenVPN服务已启动"
        else
            echo -e "${RED}openvpn命令不存在，请安装OpenVPN${NC}"
        fi
    else
        echo -e "${RED}OpenVPN配置文件不存在${NC}"
    fi
}

# 停止OpenVPN
stop_openvpn() {
    echo -e "${BLUE}停止OpenVPN...${NC}"
    if [ -f "$CONFIG_DIR/openvpn.pid" ]; then
        sudo kill $(cat "$CONFIG_DIR/openvpn.pid") 2>/dev/null
        rm -f "$CONFIG_DIR/openvpn.pid"
        echo -e "${GREEN}OpenVPN已停止${NC}"
        log_message "OpenVPN服务已停止"
    else
        sudo pkill -f openvpn 2>/dev/null
        echo -e "${YELLOW}尝试强制停止OpenVPN进程${NC}"
    fi
}

# 启动WireGuard
start_wireguard() {
    if [ -f "$CONFIG_DIR/wg0.conf" ]; then
        echo -e "${BLUE}启动WireGuard...${NC}"
        if command -v wg-quick >/dev/null 2>&1; then
            sudo cp "$CONFIG_DIR/wg0.conf" /etc/wireguard/
            sudo wg-quick up wg0
            echo -e "${GREEN}WireGuard已启动${NC}"
            log_message "WireGuard服务已启动"
        else
            echo -e "${RED}wg-quick命令不存在，请安装WireGuard${NC}"
        fi
    else
        echo -e "${RED}WireGuard配置文件不存在${NC}"
    fi
}

# 停止WireGuard
stop_wireguard() {
    echo -e "${BLUE}停止WireGuard...${NC}"
    if command -v wg-quick >/dev/null 2>&1; then
        sudo wg-quick down wg0 2>/dev/null
        echo -e "${GREEN}WireGuard已停止${NC}"
        log_message "WireGuard服务已停止"
    else
        echo -e "${RED}wg-quick命令不存在${NC}"
    fi
}

# 查看服务状态
show_service_status() {
    echo -e "${BLUE}代理服务状态${NC}"
    echo ""

    # 检查各种服务的运行状态
    echo "服务运行状态:"

    if pgrep -f ss-local >/dev/null; then
        echo -e "  Shadowsocks: ${GREEN}运行中${NC}"
    else
        echo -e "  Shadowsocks: ${RED}已停止${NC}"
    fi

    if pgrep -f v2ray >/dev/null; then
        echo -e "  V2Ray: ${GREEN}运行中${NC}"
    else
        echo -e "  V2Ray: ${RED}已停止${NC}"
    fi

    if pgrep -f trojan >/dev/null; then
        echo -e "  Trojan: ${GREEN}运行中${NC}"
    else
        echo -e "  Trojan: ${RED}已停止${NC}"
    fi

    if pgrep -f openvpn >/dev/null; then
        echo -e "  OpenVPN: ${GREEN}运行中${NC}"
    else
        echo -e "  OpenVPN: ${RED}已停止${NC}"
    fi

    if command -v wg >/dev/null 2>&1 && wg show wg0 >/dev/null 2>&1; then
        echo -e "  WireGuard: ${GREEN}运行中${NC}"
    else
        echo -e "  WireGuard: ${RED}已停止${NC}"
    fi

    echo ""
    echo "端口监听状态:"
    if command -v netstat >/dev/null 2>&1; then
        netstat -tlnp 2>/dev/null | grep :1080 && echo -e "  SOCKS代理端口1080: ${GREEN}监听中${NC}" || echo -e "  SOCKS代理端口1080: ${RED}未监听${NC}"
    fi
}

# 查看日志
show_logs() {
    echo -e "${BLUE}查看日志${NC}"
    echo ""

    if [ -f "$LOG_FILE" ]; then
        echo "最近20条日志记录:"
        echo ""
        tail -20 "$LOG_FILE"
    else
        echo -e "${YELLOW}日志文件不存在${NC}"
    fi

    echo ""
    read -p "按回车键继续..."
}

# 备份/恢复配置
backup_restore_config() {
    echo -e "${BLUE}备份/恢复配置${NC}"
    echo ""

    echo "请选择操作:"
    echo "1. 备份当前配置"
    echo "2. 恢复配置"
    echo "3. 列出备份文件"
    echo "4. 删除备份文件"

    read -p "请选择 [1-4]: " backup_choice

    case $backup_choice in
        1) backup_config ;;
        2) restore_config ;;
        3) list_backups ;;
        4) delete_backup ;;
        *) echo -e "${RED}无效选项${NC}" ;;
    esac
}

# 备份配置
backup_config() {
    timestamp=$(date +"%Y%m%d_%H%M%S")
    backup_file="$BACKUP_DIR/config_backup_$timestamp.tar.gz"

    echo -e "${BLUE}备份配置到: $backup_file${NC}"

    cd "$CONFIG_DIR"
    tar -czf "$backup_file" *.json *.conf *.ovpn 2>/dev/null

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}配置备份成功${NC}"
        log_message "配置已备份到: $backup_file"
    else
        echo -e "${RED}配置备份失败${NC}"
    fi
}

# 恢复配置
restore_config() {
    echo -e "${BLUE}可用的备份文件:${NC}"
    ls -la "$BACKUP_DIR"/config_backup_*.tar.gz 2>/dev/null | nl

    echo ""
    read -p "请输入要恢复的备份文件编号: " backup_num

    backup_file=$(ls "$BACKUP_DIR"/config_backup_*.tar.gz 2>/dev/null | sed -n "${backup_num}p")

    if [ -n "$backup_file" ] && [ -f "$backup_file" ]; then
        echo -e "${BLUE}恢复配置从: $backup_file${NC}"

        cd "$CONFIG_DIR"
        tar -xzf "$backup_file"

        if [ $? -eq 0 ]; then
            echo -e "${GREEN}配置恢复成功${NC}"
            log_message "配置已从备份恢复: $backup_file"
        else
            echo -e "${RED}配置恢复失败${NC}"
        fi
    else
        echo -e "${RED}无效的备份文件${NC}"
    fi
}

# 列出备份文件
list_backups() {
    echo -e "${BLUE}备份文件列表:${NC}"
    echo ""

    if ls "$BACKUP_DIR"/config_backup_*.tar.gz >/dev/null 2>&1; then
        ls -lah "$BACKUP_DIR"/config_backup_*.tar.gz
    else
        echo -e "${YELLOW}没有找到备份文件${NC}"
    fi
}

# 删除备份文件
delete_backup() {
    echo -e "${BLUE}可用的备份文件:${NC}"
    ls -la "$BACKUP_DIR"/config_backup_*.tar.gz 2>/dev/null | nl

    echo ""
    read -p "请输入要删除的备份文件编号: " backup_num

    backup_file=$(ls "$BACKUP_DIR"/config_backup_*.tar.gz 2>/dev/null | sed -n "${backup_num}p")

    if [ -n "$backup_file" ] && [ -f "$backup_file" ]; then
        read -p "确认删除备份文件 $(basename "$backup_file")? (y/N): " confirm

        if [[ $confirm =~ ^[Yy]$ ]]; then
            rm -f "$backup_file"
            echo -e "${GREEN}备份文件已删除${NC}"
            log_message "备份文件已删除: $backup_file"
        else
            echo -e "${YELLOW}操作已取消${NC}"
        fi
    else
        echo -e "${RED}无效的备份文件${NC}"
    fi
}

# 主程序循环
main() {
    # 检查依赖
    check_dependencies

    while true; do
        show_header
        show_menu

        read choice
        echo ""

        case $choice in
            1) configure_shadowsocks ;;
            2) configure_v2ray ;;
            3) configure_trojan ;;
            4) configure_openvpn ;;
            5) configure_wireguard ;;
            6) test_network_latency ;;
            7) show_current_status ;;
            8) set_global_proxy ;;
            9) restore_smart_proxy ;;
            10) manage_proxy_service ;;
            11) show_logs ;;
            12) backup_restore_config ;;
            0)
                echo -e "${GREEN}感谢使用VPN客户端配置工具！${NC}"
                log_message "程序退出"
                exit 0
                ;;
            *)
                echo -e "${RED}无效选项，请重新选择${NC}"
                ;;
        esac

        echo ""
        read -p "按回车键继续..."
    done
}

# 检查依赖
check_dependencies() {
    echo -e "${BLUE}检查系统依赖...${NC}"

    # 检查必要的命令
    local missing_deps=()

    if ! command -v ping >/dev/null 2>&1; then
        missing_deps+=("ping")
    fi

    if ! command -v iptables >/dev/null 2>&1; then
        missing_deps+=("iptables")
    fi

    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo -e "${YELLOW}警告: 以下依赖缺失: ${missing_deps[*]}${NC}"
        echo -e "${YELLOW}某些功能可能无法正常工作${NC}"
        log_message "缺失依赖: ${missing_deps[*]}"
    fi
}

# 启动主程序
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
