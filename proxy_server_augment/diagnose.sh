#!/bin/bash

# 设置脚本为可执行
chmod +x "$0" 2>/dev/null || true

# VPN连接诊断脚本
# 用于排查VPN连接和网络延迟测试问题

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${BLUE}=== VPN连接诊断工具 ===${NC}"
echo ""

# 1. 系统信息
echo -e "${CYAN}1. 系统信息${NC}"
echo "操作系统: $(uname -s)"
echo "内核版本: $(uname -r)"
echo "发行版信息:"
if [[ -f /etc/os-release ]]; then
    source /etc/os-release
    echo "  名称: $NAME"
    echo "  版本: $VERSION"
elif [[ -f /etc/redhat-release ]]; then
    echo "  $(cat /etc/redhat-release)"
elif [[ -f /etc/debian_version ]]; then
    echo "  Debian $(cat /etc/debian_version)"
fi
echo ""

# 2. 检查必要工具
echo -e "${CYAN}2. 检查必要工具${NC}"
tools=("ping" "ip" "iptables" "dig" "curl" "openvpn")
for tool in "${tools[@]}"; do
    if command -v "$tool" >/dev/null 2>&1; then
        version=$(command -v "$tool" && $tool --version 2>/dev/null | head -1 || echo "版本信息不可用")
        echo -e "${GREEN}✓ $tool${NC} - $version"
    else
        echo -e "${RED}✗ $tool 未安装${NC}"
    fi
done
echo ""

# 3. 检查VPN进程
echo -e "${CYAN}3. 检查VPN相关进程${NC}"
vpn_processes=("openvpn" "wireguard" "wg-quick" "strongswan" "ipsec")
found_processes=()

for process in "${vpn_processes[@]}"; do
    if pgrep -f "$process" >/dev/null; then
        echo -e "${GREEN}✓ $process 进程正在运行${NC}"
        found_processes+=("$process")
        # 显示进程详情
        ps aux | grep "$process" | grep -v grep | head -2
    fi
done

if [[ ${#found_processes[@]} -eq 0 ]]; then
    echo -e "${RED}✗ 未检测到VPN进程${NC}"
fi
echo ""

# 4. 检查网络接口
echo -e "${CYAN}4. 检查网络接口${NC}"
if command -v ip >/dev/null 2>&1; then
    echo "所有网络接口:"
    ip link show | grep -E "^[0-9]+:" | head -10
    echo ""
    
    echo "VPN相关接口:"
    vpn_interfaces=$(ip link show | grep -E "tun[0-9]+|tap[0-9]+|wg[0-9]+" | awk -F: '{print $2}' | tr -d ' ')
    if [[ -n "$vpn_interfaces" ]]; then
        for interface in $vpn_interfaces; do
            status=$(ip link show "$interface" | grep -o "state [A-Z]*" | awk '{print $2}')
            ip_addr=$(ip addr show "$interface" | grep "inet " | awk '{print $2}' | head -1)
            echo -e "${GREEN}✓ $interface${NC} - 状态: $status, IP: $ip_addr"
        done
    else
        echo -e "${YELLOW}⚠ 未找到VPN网络接口${NC}"
    fi
else
    echo -e "${RED}✗ ip命令不可用${NC}"
fi
echo ""

# 5. 检查路由表
echo -e "${CYAN}5. 检查路由表${NC}"
if command -v ip >/dev/null 2>&1; then
    echo "默认路由:"
    ip route show default
    echo ""
    
    echo "VPN相关路由:"
    ip route show | grep -E "tun[0-9]+|tap[0-9]+|wg[0-9]+" | head -5
    if [[ $? -ne 0 ]]; then
        echo -e "${YELLOW}⚠ 未找到VPN路由${NC}"
    fi
else
    echo -e "${RED}✗ 无法检查路由表${NC}"
fi
echo ""

# 6. 检查DNS配置
echo -e "${CYAN}6. 检查DNS配置${NC}"
if [[ -f /etc/resolv.conf ]]; then
    echo "当前DNS配置:"
    cat /etc/resolv.conf | grep nameserver
else
    echo -e "${RED}✗ 无法读取DNS配置${NC}"
fi
echo ""

# 7. 测试网络连接
echo -e "${CYAN}7. 测试网络连接${NC}"

# 测试本地连接
echo -n "本地回环测试: "
if ping -c 1 -W 2 127.0.0.1 >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 正常${NC}"
else
    echo -e "${RED}✗ 失败${NC}"
fi

# 测试DNS服务器
echo -n "DNS服务器连接 (*******): "
if ping -c 1 -W 3 ******* >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 正常${NC}"
else
    echo -e "${RED}✗ 失败${NC}"
fi

# 测试域名解析
echo -n "域名解析测试 (google.com): "
if command -v dig >/dev/null 2>&1; then
    if dig +short google.com >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 正常${NC}"
    else
        echo -e "${RED}✗ 失败${NC}"
    fi
elif command -v nslookup >/dev/null 2>&1; then
    if nslookup google.com >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 正常${NC}"
    else
        echo -e "${RED}✗ 失败${NC}"
    fi
else
    echo -e "${YELLOW}⚠ DNS工具不可用${NC}"
fi

# 测试HTTP连接
echo -n "HTTP连接测试 (google.com): "
if command -v curl >/dev/null 2>&1; then
    if curl -s --max-time 10 http://google.com >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 正常${NC}"
    else
        echo -e "${RED}✗ 失败${NC}"
    fi
else
    echo -e "${YELLOW}⚠ curl不可用${NC}"
fi

# 测试HTTPS连接
echo -n "HTTPS连接测试 (google.com): "
if command -v curl >/dev/null 2>&1; then
    if curl -s --max-time 10 https://google.com >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 正常${NC}"
    else
        echo -e "${RED}✗ 失败${NC}"
    fi
else
    echo -e "${YELLOW}⚠ curl不可用${NC}"
fi
echo ""

# 8. 检查外部IP
echo -e "${CYAN}8. 检查外部IP地址${NC}"
if command -v curl >/dev/null 2>&1; then
    echo -n "当前外部IP: "
    external_ip=$(curl -s --max-time 10 https://api.ipify.org 2>/dev/null)
    if [[ -n "$external_ip" ]]; then
        echo -e "${GREEN}$external_ip${NC}"
    else
        echo -e "${RED}无法获取${NC}"
    fi
else
    echo -e "${YELLOW}⚠ curl不可用，无法检测外部IP${NC}"
fi
echo ""

# 9. 延迟测试
echo -e "${CYAN}9. 网络延迟测试${NC}"
test_targets="google.com baidu.com qq.com"

for target in $test_targets; do
    echo -n "测试 $target: "
    if command -v ping >/dev/null 2>&1; then
        ping_result=$(ping -c 3 -W 5 "$target" 2>/dev/null)
        if [[ $? -eq 0 ]]; then
            # 尝试提取延迟
            latency=$(echo "$ping_result" | grep "min/avg/max" | awk -F '/' '{print $5}' | sed 's/[^0-9.]//g')
            if [[ -z "$latency" ]]; then
                latency=$(echo "$ping_result" | tail -1 | awk '{print $4}' | cut -d'/' -f2)
            fi
            
            if [[ -n "$latency" ]]; then
                echo -e "${GREEN}${latency}ms${NC}"
            else
                echo -e "${YELLOW}延迟数据解析失败${NC}"
                echo "原始输出:"
                echo "$ping_result" | tail -2
            fi
        else
            echo -e "${RED}连接失败${NC}"
        fi
    else
        echo -e "${RED}ping不可用${NC}"
    fi
done
echo ""

# 10. 防火墙状态
echo -e "${CYAN}10. 防火墙状态${NC}"
if command -v iptables >/dev/null 2>&1; then
    rule_count=$(iptables -L | wc -l)
    echo "iptables规则数量: $rule_count"
    
    if [[ $rule_count -gt 20 ]]; then
        echo -e "${GREEN}✓ 防火墙规则已配置${NC}"
    else
        echo -e "${YELLOW}⚠ 防火墙规则较少${NC}"
    fi
else
    echo -e "${RED}✗ iptables不可用${NC}"
fi
echo ""

echo -e "${BLUE}=== 诊断完成 ===${NC}"
echo ""
echo -e "${YELLOW}问题排查建议:${NC}"
echo "1. 如果VPN进程未运行，请检查VPN配置和启动脚本"
echo "2. 如果网络接口未找到，VPN可能未正确建立连接"
echo "3. 如果延迟测试失败，检查DNS解析和网络连接"
echo "4. 如果外部IP未改变，VPN可能未正确路由流量"
echo "5. 检查防火墙规则是否阻止了VPN流量"
