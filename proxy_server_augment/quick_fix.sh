#!/bin/bash

# 快速修复脚本 - 解决OpenVPN版本和DNS泄漏防护问题

set -euo pipefail

# 设置脚本权限
chmod +x "$0" 2>/dev/null || true

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo -e "${BLUE}=== VPN快速修复工具 ===${NC}"
echo ""

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
    echo -e "${RED}错误: 需要root权限${NC}"
    echo -e "${YELLOW}请使用sudo运行: sudo $0${NC}"
    exit 1
fi

# 1. 检查当前OpenVPN版本
echo -e "${CYAN}1. 检查OpenVPN版本${NC}"
if command -v openvpn >/dev/null 2>&1; then
    current_version=$(openvpn --version | head -1 | awk '{print $2}')
    echo "当前版本: $current_version"
    
    # 检查版本是否支持block-outside-dns
    version_num=$(echo "$current_version" | sed 's/[^0-9.]//g')
    major=$(echo "$version_num" | cut -d. -f1)
    minor=$(echo "$version_num" | cut -d. -f2)
    
    if [[ $major -gt 2 ]] || [[ $major -eq 2 && $minor -ge 6 ]]; then
        echo -e "${GREEN}✓ 支持 block-outside-dns${NC}"
        NEEDS_UPGRADE=false
    else
        echo -e "${RED}✗ 不支持 block-outside-dns${NC}"
        NEEDS_UPGRADE=true
    fi
else
    echo -e "${RED}OpenVPN未安装${NC}"
    NEEDS_UPGRADE=true
fi
echo ""

# 2. 升级OpenVPN（如果需要）
if [[ "$NEEDS_UPGRADE" == true ]]; then
    echo -e "${CYAN}2. 升级OpenVPN${NC}"
    echo -e "${YELLOW}需要升级OpenVPN以支持DNS泄漏防护${NC}"
    
    read -p "是否现在升级OpenVPN? (y/N): " upgrade_confirm
    if [[ $upgrade_confirm =~ ^[Yy]$ ]]; then
        # 停止当前VPN连接
        echo "停止当前VPN连接..."
        pkill -f openvpn || true
        sleep 2
        
        # 检测操作系统并升级
        if [[ -f /etc/os-release ]]; then
            source /etc/os-release
            case $ID in
                ubuntu|debian)
                    echo "为Ubuntu/Debian升级OpenVPN..."

                    # 检查是否在容器中
                    if [[ -f /.dockerenv ]] || grep -q docker /proc/1/cgroup 2>/dev/null; then
                        echo -e "${BLUE}检测到Docker容器环境，使用特殊升级方法${NC}"

                        # 在容器中，先移除旧版本
                        apt-get remove -y openvpn openvpn-systemd-resolved || true
                        apt-get autoremove -y || true

                        # 更新包列表
                        apt-get update

                        # 安装编译依赖
                        apt-get install -y build-essential libssl-dev liblzo2-dev libpam0g-dev \
                            libpkcs11-helper1-dev libsystemd-dev pkg-config autoconf automake \
                            libtool wget ca-certificates

                        # 从源码编译最新版本
                        compile_openvpn_from_source
                    else
                        # 非容器环境，使用仓库安装
                        apt-get update
                        apt-get install -y software-properties-common apt-transport-https ca-certificates gnupg curl

                        # 添加OpenVPN官方仓库
                        curl -fsSL https://swupdate.openvpn.net/repos/openvpn-repo-pkg-key.pub | gpg --dearmor > /etc/apt/trusted.gpg.d/openvpn.gpg
                        echo "deb https://build.openvpn.net/debian/openvpn/stable jammy main" > /etc/apt/sources.list.d/openvpn.list

                        apt-get update
                        apt-get install -y openvpn
                    fi
                    ;;
                centos|rhel)
                    echo "为CentOS/RHEL升级OpenVPN..."
                    yum install -y epel-release || true

                    cat > /etc/yum.repos.d/openvpn.repo << 'EOF'
[openvpn]
name=OpenVPN Repository
baseurl=https://build.openvpn.net/RHEL$releasever/$basearch/
enabled=1
gpgcheck=1
gpgkey=https://swupdate.openvpn.net/repos/openvpn-repo-pkg-key.pub
EOF
                    yum install -y openvpn
                    ;;
                *)
                    echo -e "${YELLOW}不支持的操作系统，尝试从源码编译${NC}"
                    compile_openvpn_from_source
                    ;;
            esac
            
            # 验证升级
            if command -v openvpn >/dev/null 2>&1; then
                new_version=$(openvpn --version | head -1 | awk '{print $2}')
                echo -e "${GREEN}✓ OpenVPN升级成功: $new_version${NC}"
            else
                echo -e "${RED}✗ OpenVPN升级失败${NC}"
                exit 1
            fi
        fi
    else
        echo -e "${YELLOW}跳过OpenVPN升级${NC}"
    fi
    echo ""
fi

# 从源码编译OpenVPN函数
compile_openvpn_from_source() {
    echo -e "${BLUE}从源码编译OpenVPN 2.6.12...${NC}"

    local openvpn_version="2.6.12"
    local build_dir="/tmp/openvpn-build"

    # 创建构建目录
    rm -rf "$build_dir"
    mkdir -p "$build_dir"
    cd "$build_dir"

    # 下载源码
    echo "下载OpenVPN $openvpn_version 源码..."
    wget -q "https://swupdate.openvpn.org/community/releases/openvpn-${openvpn_version}.tar.gz"

    if [[ ! -f "openvpn-${openvpn_version}.tar.gz" ]]; then
        echo -e "${RED}下载失败，尝试备用链接...${NC}"
        wget -q "https://github.com/OpenVPN/openvpn/releases/download/v${openvpn_version}/openvpn-${openvpn_version}.tar.gz"
    fi

    if [[ ! -f "openvpn-${openvpn_version}.tar.gz" ]]; then
        echo -e "${RED}无法下载OpenVPN源码${NC}"
        return 1
    fi

    tar -xzf "openvpn-${openvpn_version}.tar.gz"
    cd "openvpn-${openvpn_version}"

    # 配置编译选项
    echo "配置编译选项..."
    ./configure \
        --enable-iproute2 \
        --enable-plugins \
        --enable-plugin-auth-pam \
        --enable-plugin-down-root \
        --enable-systemd \
        --prefix=/usr \
        --sysconfdir=/etc \
        --localstatedir=/var

    # 编译
    echo "编译OpenVPN（这可能需要几分钟）..."
    make -j$(nproc) || make

    # 安装
    echo "安装OpenVPN..."
    make install

    # 创建必要的目录和文件
    mkdir -p /etc/openvpn/client /etc/openvpn/server
    mkdir -p /var/log/openvpn

    # 创建systemd服务文件
    cat > /etc/systemd/system/openvpn@.service << 'EOF'
[Unit]
Description=OpenVPN connection to %i
PartOf=openvpn.service
ReloadPropagatedFrom=openvpn.service
Before=systemd-user-sessions.service
After=network-online.target
Wants=network-online.target
Documentation=man:openvpn(8)
Documentation=https://community.openvpn.net/openvpn/wiki/Openvpn24ManPage
Documentation=https://community.openvpn.net/openvpn/wiki/HOWTO

[Service]
Type=notify
PrivateTmp=true
WorkingDirectory=/etc/openvpn/client
ExecStart=/usr/sbin/openvpn --suppress-timestamps --nobind --config %i.conf
CapabilityBoundingSet=CAP_IPC_LOCK CAP_NET_ADMIN CAP_NET_RAW CAP_SETGID CAP_SETUID CAP_SYS_CHROOT CAP_DAC_OVERRIDE
LimitNPROC=10
DeviceAllow=/dev/null rw
DeviceAllow=/dev/net/tun rw
ProtectSystem=true
ProtectHome=true
KillMode=process
RestartSec=5s
Restart=on-failure

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd
    systemctl daemon-reload || true

    # 清理构建目录
    cd /
    rm -rf "$build_dir"

    echo -e "${GREEN}✓ OpenVPN编译安装完成${NC}"
}

# 3. 检查和修复VPN配置
echo -e "${CYAN}3. 检查VPN配置${NC}"
config_dir="$SCRIPT_DIR/configs/openvpn"

if [[ -d "$config_dir" ]]; then
    ovpn_files=($(find "$config_dir" -name "*.ovpn" 2>/dev/null))
    
    if [[ ${#ovpn_files[@]} -gt 0 ]]; then
        echo "找到 ${#ovpn_files[@]} 个配置文件"
        
        for config_file in "${ovpn_files[@]}"; do
            echo "检查配置: $(basename "$config_file")"
            
            # 检查是否包含block-outside-dns
            if grep -q "block-outside-dns" "$config_file"; then
                echo -e "  ${GREEN}✓ 包含DNS泄漏防护${NC}"
            else
                echo -e "  ${YELLOW}⚠ 缺少DNS泄漏防护，正在添加...${NC}"
                
                # 添加DNS泄漏防护
                cat >> "$config_file" << 'EOF'

# DNS泄漏防护 - 由快速修复脚本添加
ignore-unknown-option block-outside-dns
block-outside-dns
EOF
                echo -e "  ${GREEN}✓ 已添加DNS泄漏防护${NC}"
            fi
        done
    else
        echo -e "${YELLOW}未找到OpenVPN配置文件${NC}"
        echo "请先导入.ovpn配置文件"
    fi
else
    echo -e "${YELLOW}OpenVPN配置目录不存在${NC}"
    mkdir -p "$config_dir"
fi
echo ""

# 4. 检查和修复容器环境
echo -e "${CYAN}4. 检查容器环境${NC}"

if [[ -f /.dockerenv ]] || grep -q docker /proc/1/cgroup 2>/dev/null; then
    echo -e "${BLUE}检测到Docker容器环境${NC}"

    # 检查容器权限
    echo -n "检查特权模式: "
    if [[ -c /dev/net/tun ]]; then
        echo -e "${GREEN}✓ 有TUN设备访问权限${NC}"
    else
        echo -e "${RED}✗ 缺少TUN设备权限${NC}"
        echo -e "${YELLOW}容器需要以特权模式运行或添加--cap-add=NET_ADMIN --device=/dev/net/tun${NC}"

        # 尝试创建TUN设备
        if ! [[ -c /dev/net/tun ]]; then
            echo "尝试创建TUN设备..."
            mkdir -p /dev/net
            mknod /dev/net/tun c 10 200 2>/dev/null || echo -e "${YELLOW}无法创建TUN设备，需要特权权限${NC}"
            chmod 666 /dev/net/tun 2>/dev/null || true
        fi
    fi

    # 检查网络命名空间权限
    echo -n "检查网络权限: "
    if ip link add dummy0 type dummy 2>/dev/null; then
        ip link delete dummy0 2>/dev/null
        echo -e "${GREEN}✓ 有网络管理权限${NC}"
    else
        echo -e "${RED}✗ 缺少网络管理权限${NC}"
        echo -e "${YELLOW}容器需要--cap-add=NET_ADMIN权限${NC}"
    fi

    # 检查iptables权限
    echo -n "检查防火墙权限: "
    if iptables -L >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 有iptables权限${NC}"
    else
        echo -e "${RED}✗ 缺少iptables权限${NC}"
        echo -e "${YELLOW}容器需要--cap-add=NET_ADMIN权限${NC}"
    fi

    echo ""
    echo -e "${YELLOW}Docker容器VPN运行建议:${NC}"
    echo "docker run --privileged --cap-add=NET_ADMIN --device=/dev/net/tun ..."
    echo "或者使用: docker run --privileged ..."
    echo ""
else
    echo -e "${GREEN}运行在标准Linux环境中${NC}"
fi
echo ""

# 5. 测试网络连接
echo -e "${CYAN}5. 测试网络连接${NC}"

# 测试基本连接
echo -n "测试DNS解析: "
if dig +short google.com >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 正常${NC}"
else
    echo -e "${RED}✗ 失败${NC}"
fi

echo -n "测试HTTP连接: "
if curl -s --max-time 10 http://www.google.com >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 正常${NC}"
else
    echo -e "${RED}✗ 失败${NC}"
fi

echo -n "测试HTTPS连接: "
if curl -s --max-time 10 https://www.google.com >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 正常${NC}"
else
    echo -e "${RED}✗ 失败${NC}"
fi

# 检查外部IP
echo -n "检查外部IP: "
external_ip=$(curl -s --max-time 10 https://api.ipify.org 2>/dev/null || echo "")
if [[ -n "$external_ip" ]]; then
    echo -e "${GREEN}$external_ip${NC}"
else
    echo -e "${RED}无法获取${NC}"
fi
echo ""

# 6. 提供修复建议
echo -e "${CYAN}6. 修复建议${NC}"

if [[ -z "$(pgrep -f openvpn)" ]]; then
    echo -e "${YELLOW}VPN未连接，请执行以下步骤:${NC}"

    if [[ -f /.dockerenv ]] || grep -q docker /proc/1/cgroup 2>/dev/null; then
        echo -e "${BLUE}容器环境特殊步骤:${NC}"
        echo "1. 运行容器修复: sudo ./fix_container_vpn.sh"
        echo "2. 确保容器有足够权限 (--privileged 或 --cap-add=NET_ADMIN)"
        echo "3. 重新启动容器（如果需要）"
        echo "4. 运行主程序: sudo ./server_proxy.sh"
    else
        echo "1. 运行主程序: sudo ./server_proxy.sh"
    fi

    echo "2. 选择选项2连接VPN"
    echo "3. 选择配置文件"
    echo "4. 等待连接建立"
else
    echo -e "${GREEN}VPN进程正在运行${NC}"

    # 检查VPN接口
    if ip link show | grep -E "tun[0-9]+|tap[0-9]+" >/dev/null; then
        echo -e "${GREEN}✓ VPN接口已创建${NC}"

        # 检查接口状态
        vpn_interface=$(ip link show | grep -E "tun[0-9]+" | head -1 | awk -F: '{print $2}' | tr -d ' ')
        if [[ -n "$vpn_interface" ]]; then
            vpn_ip=$(ip addr show "$vpn_interface" | grep "inet " | awk '{print $2}' | head -1)
            echo -e "${GREEN}VPN接口: $vpn_interface ($vpn_ip)${NC}"
        fi
    else
        echo -e "${RED}✗ VPN接口未创建${NC}"

        if [[ -f /.dockerenv ]] || grep -q docker /proc/1/cgroup 2>/dev/null; then
            echo -e "${YELLOW}容器环境问题排查:${NC}"
            echo "1. 检查容器权限: docker run --privileged ..."
            echo "2. 检查TUN设备: ls -la /dev/net/tun"
            echo "3. 运行容器修复: sudo ./fix_container_vpn.sh"
            echo "4. 重启VPN连接"
        else
            echo -e "${YELLOW}标准环境问题排查:${NC}"
            echo "1. 重启VPN连接"
            echo "2. 检查配置文件是否正确"
            echo "3. 查看OpenVPN日志"
            echo "4. 检查系统权限"
        fi
    fi
fi

echo ""
echo -e "${BLUE}=== 快速修复完成 ===${NC}"
echo ""
echo -e "${YELLOW}如果问题仍然存在，请:${NC}"
echo "1. 运行完整诊断: ./diagnose.sh"
echo "2. 检查防火墙设置"
echo "3. 验证VPN服务器配置"
echo "4. 查看详细日志文件"
