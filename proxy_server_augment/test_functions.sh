#!/bin/bash

# 功能测试脚本
# 用于验证各个模块的基本功能

set -euo pipefail

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 测试计数器
TESTS_PASSED=0
TESTS_FAILED=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo -n "测试 $test_name: "
    
    if eval "$test_command" >/dev/null 2>&1; then
        echo -e "${GREEN}通过${NC}"
        ((TESTS_PASSED++))
        return 0
    else
        echo -e "${RED}失败${NC}"
        ((TESTS_FAILED++))
        return 1
    fi
}

# 测试脚本语法
test_syntax() {
    echo -e "${BLUE}=== 语法测试 ===${NC}"
    
    run_test "主脚本语法" "bash -n '$SCRIPT_DIR/server_proxy.sh'"
    run_test "OpenVPN模块语法" "bash -n '$SCRIPT_DIR/modules/openvpn_core.sh'"
    run_test "安全模块语法" "bash -n '$SCRIPT_DIR/modules/security_core.sh'"
    run_test "网络测试模块语法" "bash -n '$SCRIPT_DIR/modules/network_test.sh'"
    run_test "安装脚本语法" "bash -n '$SCRIPT_DIR/install.sh'"
    
    echo ""
}

# 测试目录结构
test_directory_structure() {
    echo -e "${BLUE}=== 目录结构测试 ===${NC}"
    
    run_test "主脚本存在" "test -f '$SCRIPT_DIR/server_proxy.sh'"
    run_test "modules目录存在" "test -d '$SCRIPT_DIR/modules'"
    run_test "configs目录存在" "test -d '$SCRIPT_DIR/configs'"
    run_test "logs目录存在" "test -d '$SCRIPT_DIR/logs'"
    run_test "backup目录存在" "test -d '$SCRIPT_DIR/backup'"
    run_test "README文件存在" "test -f '$SCRIPT_DIR/README.md'"
    run_test "安装脚本存在" "test -f '$SCRIPT_DIR/install.sh'"
    
    echo ""
}

# 测试文件权限
test_permissions() {
    echo -e "${BLUE}=== 权限测试 ===${NC}"
    
    run_test "主脚本可执行" "test -x '$SCRIPT_DIR/server_proxy.sh'"
    run_test "安装脚本可执行" "test -x '$SCRIPT_DIR/install.sh'"
    run_test "OpenVPN模块可执行" "test -x '$SCRIPT_DIR/modules/openvpn_core.sh'"
    run_test "安全模块可执行" "test -x '$SCRIPT_DIR/modules/security_core.sh'"
    run_test "网络测试模块可执行" "test -x '$SCRIPT_DIR/modules/network_test.sh'"
    
    echo ""
}

# 测试模块加载
test_module_loading() {
    echo -e "${BLUE}=== 模块加载测试 ===${NC}"

    # 设置临时环境变量
    export USER_CONFIG_DIR="/tmp/vpn_test_$$"
    export CONFIGS_DIR="$SCRIPT_DIR/configs"
    export LOGS_DIR="$SCRIPT_DIR/logs"
    export BACKUP_DIR="$SCRIPT_DIR/backup"
    mkdir -p "$USER_CONFIG_DIR" "$CONFIGS_DIR" "$LOGS_DIR" "$BACKUP_DIR"

    # 测试OpenVPN模块
    run_test "OpenVPN模块加载" "source '$SCRIPT_DIR/modules/openvpn_core.sh' && declare -f check_openvpn_installation >/dev/null"

    # 测试安全模块
    run_test "安全模块加载" "source '$SCRIPT_DIR/modules/security_core.sh' && declare -f init_security_module >/dev/null"

    # 测试网络测试模块
    run_test "网络测试模块加载" "source '$SCRIPT_DIR/modules/network_test.sh' && declare -f test_network_latency >/dev/null"

    # 清理
    rm -rf "$USER_CONFIG_DIR"

    echo ""
}

# 测试基本功能
test_basic_functions() {
    echo -e "${BLUE}=== 基本功能测试 ===${NC}"
    
    # 创建临时配置目录
    local temp_config_dir="/tmp/vpn_test_$$"
    mkdir -p "$temp_config_dir"
    
    # 设置环境变量
    export USER_CONFIG_DIR="$temp_config_dir"
    export CONFIGS_DIR="$SCRIPT_DIR/configs"
    export LOGS_DIR="$SCRIPT_DIR/logs"
    export BACKUP_DIR="$SCRIPT_DIR/backup"
    
    # 测试目录创建
    run_test "配置目录创建" "mkdir -p '$temp_config_dir' && test -d '$temp_config_dir'"
    
    # 测试日志功能
    run_test "日志功能" "source '$SCRIPT_DIR/modules/openvpn_core.sh' && log_message 3 'Test message' && test -f '$SCRIPT_DIR/logs/vpn-client.log'"
    
    # 清理
    rm -rf "$temp_config_dir"
    
    echo ""
}

# 测试网络工具
test_network_tools() {
    echo -e "${BLUE}=== 网络工具测试 ===${NC}"
    
    run_test "ping命令可用" "command -v ping >/dev/null"
    run_test "ip命令可用" "command -v ip >/dev/null"
    run_test "dig命令可用" "command -v dig >/dev/null"
    run_test "curl命令可用" "command -v curl >/dev/null"
    
    # 测试网络连接
    run_test "网络连接测试" "ping -c 1 -W 3 ******* >/dev/null"
    
    echo ""
}

# 测试配置文件处理
test_config_handling() {
    echo -e "${BLUE}=== 配置文件处理测试 ===${NC}"
    
    # 创建测试配置文件
    local test_config="/tmp/test_vpn_$$.ovpn"
    cat > "$test_config" << 'EOF'
client
dev tun
proto udp
remote test.example.com 1194
resolv-retry infinite
nobind
persist-key
persist-tun
ca ca.crt
cert client.crt
key client.key
remote-cert-tls server
cipher AES-256-CBC
verb 3
EOF
    
    # 设置环境变量
    export CONFIGS_DIR="$SCRIPT_DIR/configs"
    export LOGS_DIR="$SCRIPT_DIR/logs"
    export USER_CONFIG_DIR="/tmp/vpn_test_$$"
    mkdir -p "$USER_CONFIG_DIR"
    
    # 测试配置验证
    run_test "配置文件验证" "source '$SCRIPT_DIR/modules/openvpn_core.sh' && validate_ovpn_file '$test_config'"
    
    # 清理
    rm -f "$test_config"
    rm -rf "$USER_CONFIG_DIR"
    
    echo ""
}

# 显示测试结果
show_test_results() {
    echo -e "${BLUE}=== 测试结果汇总 ===${NC}"
    echo ""
    echo -e "通过的测试: ${GREEN}$TESTS_PASSED${NC}"
    echo -e "失败的测试: ${RED}$TESTS_FAILED${NC}"
    echo -e "总计测试: $((TESTS_PASSED + TESTS_FAILED))"
    echo ""
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}所有测试通过！系统准备就绪。${NC}"
        return 0
    else
        echo -e "${RED}有 $TESTS_FAILED 个测试失败，请检查系统配置。${NC}"
        return 1
    fi
}

# 主测试流程
main() {
    echo -e "${BLUE}================================================================${NC}"
    echo -e "${BLUE}           Linux VPN安全客户端 - 功能测试${NC}"
    echo -e "${BLUE}================================================================${NC}"
    echo ""
    
    # 运行各项测试
    test_syntax
    test_directory_structure
    test_permissions
    test_module_loading
    test_basic_functions
    test_network_tools
    test_config_handling
    
    # 显示结果
    show_test_results
}

# 运行测试
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
