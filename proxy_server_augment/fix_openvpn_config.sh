#!/bin/bash

# OpenVPN配置修复脚本
# 移除不兼容的选项，确保在OpenVPN 2.5.x版本中正常工作

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OPENVPN_CONFIG_DIR="$SCRIPT_DIR/configs/openvpn"

echo -e "${BLUE}=== OpenVPN配置修复工具 ===${NC}"
echo ""

# 检查配置目录
if [[ ! -d "$OPENVPN_CONFIG_DIR" ]]; then
    echo -e "${RED}错误: OpenVPN配置目录不存在: $OPENVPN_CONFIG_DIR${NC}"
    exit 1
fi

# 查找所有.ovpn文件
ovpn_files=($(find "$OPENVPN_CONFIG_DIR" -name "*.ovpn" 2>/dev/null))

if [[ ${#ovpn_files[@]} -eq 0 ]]; then
    echo -e "${YELLOW}未找到OpenVPN配置文件${NC}"
    echo "请先导入.ovpn配置文件到: $OPENVPN_CONFIG_DIR"
    exit 1
fi

echo -e "${BLUE}找到 ${#ovpn_files[@]} 个配置文件:${NC}"
for file in "${ovpn_files[@]}"; do
    echo "  $(basename "$file")"
done
echo ""

# 不兼容的选项列表
incompatible_options=(
    "block-outside-dns"
    "pull-filter ignore redirect-gateway"
    "route-method exe"
    "route-noexec"
    "up /etc/openvpn/update-resolv-conf"
    "down /etc/openvpn/update-resolv-conf"
)

# 处理每个配置文件
for config_file in "${ovpn_files[@]}"; do
    echo -e "${BLUE}处理配置文件: $(basename "$config_file")${NC}"
    
    # 创建备份
    backup_file="${config_file}.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$config_file" "$backup_file"
    echo "  备份已创建: $(basename "$backup_file")"
    
    # 创建临时文件
    temp_file=$(mktemp)
    
    # 移除不兼容的选项
    removed_count=0
    while IFS= read -r line; do
        skip_line=false
        
        for option in "${incompatible_options[@]}"; do
            if [[ "$line" =~ ^[[:space:]]*${option} ]]; then
                echo "  移除不兼容选项: $option"
                skip_line=true
                ((removed_count++))
                break
            fi
        done
        
        if [[ "$skip_line" == false ]]; then
            echo "$line" >> "$temp_file"
        fi
    done < "$config_file"
    
    # 添加兼容的配置选项
    cat >> "$temp_file" << 'EOF'

# 兼容性配置 - 由修复脚本添加
script-security 2
dhcp-option DNS *******
dhcp-option DNS *******
redirect-gateway def1
route-delay 2
verb 3
EOF
    
    # 替换原文件
    mv "$temp_file" "$config_file"
    
    echo -e "  ${GREEN}✓ 已移除 $removed_count 个不兼容选项${NC}"
    echo ""
done

echo -e "${GREEN}=== 配置修复完成 ===${NC}"
echo ""
echo -e "${YELLOW}修复内容:${NC}"
echo "1. 移除了不兼容的 block-outside-dns 选项"
echo "2. 移除了不兼容的 pull-filter 选项"
echo "3. 移除了不兼容的 route-method 和 route-noexec 选项"
echo "4. 移除了可能不存在的脚本路径"
echo "5. 添加了基本的兼容性配置"
echo ""
echo -e "${BLUE}下一步:${NC}"
echo "1. 重新启动VPN连接"
echo "2. 运行 ./diagnose.sh 检查连接状态"
echo "3. 如果仍有问题，检查VPN服务器配置"
