#!/bin/bash

# 网络测试模块
# 提供延迟测试、IP检测、安全验证等功能

# 如果变量未定义，设置默认值
if [[ -z "${LOGS_DIR:-}" ]]; then
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
    MODULES_DIR="$SCRIPT_DIR/modules"
    LOGS_DIR="$SCRIPT_DIR/logs"

    # 颜色定义
    RED='\033[0;31m'
    GREEN='\033[0;32m'
    YELLOW='\033[1;33m'
    BLUE='\033[0;34m'
    CYAN='\033[0;36m'
    NC='\033[0m'

    # 日志级别
    LOG_ERROR=1
    LOG_WARN=2
    LOG_INFO=3
    LOG_DEBUG=4

    # 简单日志函数
    log_message() {
        local level=$1
        local message=$2
        local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
        echo "[$timestamp] [LEVEL:$level] $message" >> "$LOGS_DIR/vpn-client.log"
    }
fi

# 确保目录存在
mkdir -p "$LOGS_DIR" 2>/dev/null || true

# 测试目标配置 - 使用简单格式避免关联数组兼容性问题
TEST_TARGETS="Google:google.com QQ:qq.com Baidu:baidu.com Alibaba:alibaba.com YouTube:youtube.com GoogleSearch:www.google.com"

# IP检测服务
declare -A IP_CHECK_SERVICES=(
    ["ipify"]="https://api.ipify.org"
    ["httpbin"]="https://httpbin.org/ip"
    ["icanhazip"]="https://icanhazip.com"
    ["whatismyip"]="https://ipv4.icanhazip.com"
)

# 延迟测试
test_network_latency() {
    echo -e "${BLUE}网络延迟测试${NC}"
    echo ""
    
    log_message $LOG_INFO "开始网络延迟测试"
    
    echo -e "${YELLOW}正在测试网络延迟...${NC}"
    echo ""
    
    # 创建结果数组
    local results=()

    for target_pair in $TEST_TARGETS; do
        local name=$(echo "$target_pair" | cut -d: -f1)
        local target=$(echo "$target_pair" | cut -d: -f2)
        echo -n "测试 $name ($target): "

        local latency_result=$(test_single_latency "$target")

        if [[ $? -eq 0 && -n "$latency_result" ]]; then
            echo -e "${GREEN}${latency_result}${NC}"
            results+=("$name: $latency_result")
            log_message $LOG_INFO "延迟测试 $name: $latency_result"
        else
            echo -e "${RED}连接失败${NC}"
            results+=("$name: 连接失败")
            log_message $LOG_WARN "延迟测试 $name: 连接失败"
        fi
    done
    
    echo ""
    echo -e "${CYAN}=== 延迟测试汇总 ===${NC}"
    for result in "${results[@]}"; do
        echo "  $result"
    done
    
    echo ""
    log_message $LOG_INFO "网络延迟测试完成"
}

# 单个目标延迟测试
test_single_latency() {
    local target="$1"
    local ping_count=3
    local timeout=5

    if command -v ping >/dev/null 2>&1; then
        # macOS和Linux的ping命令参数不同
        local ping_result
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS使用-t而不是-W作为超时参数
            ping_result=$(ping -c $ping_count -t $timeout "$target" 2>/dev/null)
        else
            # Linux使用-W作为超时参数
            ping_result=$(ping -c $ping_count -W $timeout "$target" 2>/dev/null)
        fi

        if [[ $? -eq 0 ]]; then
            # 提取平均延迟
            local avg_latency
            if [[ "$OSTYPE" == "darwin"* ]]; then
                # macOS ping输出格式
                avg_latency=$(echo "$ping_result" | tail -1 | awk -F '/' '{print $5}' | sed 's/[^0-9.]//g')
            else
                # Linux ping输出格式
                avg_latency=$(echo "$ping_result" | tail -1 | awk -F '/' '{print $5}' | sed 's/[^0-9.]//g')
            fi

            if [[ -n "$avg_latency" ]]; then
                echo "${avg_latency}ms"
                return 0
            fi
        fi
    fi

    # 如果ping失败，尝试使用curl测试HTTP延迟
    if command -v curl >/dev/null 2>&1; then
        local start_time
        local end_time

        if [[ "$OSTYPE" == "darwin"* ]]; then
            start_time=$(python3 -c "import time; print(int(time.time() * 1000))" 2>/dev/null || date +%s%3N)
        else
            start_time=$(date +%s%3N)
        fi

        if curl -s --max-time $timeout "http://$target" >/dev/null 2>&1; then
            if [[ "$OSTYPE" == "darwin"* ]]; then
                end_time=$(python3 -c "import time; print(int(time.time() * 1000))" 2>/dev/null || date +%s%3N)
            else
                end_time=$(date +%s%3N)
            fi

            local latency=$((end_time - start_time))
            echo "${latency}ms (HTTP)"
            return 0
        fi
    fi

    return 1
}

# IP地址检测
check_ip_address() {
    echo -e "${BLUE}IP地址检测${NC}"
    echo ""
    
    log_message $LOG_INFO "开始IP地址检测"
    
    echo -e "${YELLOW}正在检测当前IP地址...${NC}"
    echo ""
    
    local detected_ips=()
    local successful_checks=0
    
    for service_name in "${!IP_CHECK_SERVICES[@]}"; do
        local service_url="${IP_CHECK_SERVICES[$service_name]}"
        echo -n "通过 $service_name 检测: "
        
        local ip_result=$(get_external_ip "$service_url")
        
        if [[ $? -eq 0 && -n "$ip_result" ]]; then
            echo -e "${GREEN}$ip_result${NC}"
            detected_ips+=("$ip_result")
            ((successful_checks++))
            log_message $LOG_INFO "IP检测 $service_name: $ip_result"
        else
            echo -e "${RED}检测失败${NC}"
            log_message $LOG_WARN "IP检测 $service_name: 检测失败"
        fi
    done
    
    echo ""
    
    if [[ $successful_checks -gt 0 ]]; then
        # 分析检测结果
        analyze_ip_results "${detected_ips[@]}"
    else
        echo -e "${RED}所有IP检测服务都失败了${NC}"
        echo -e "${YELLOW}可能的原因:${NC}"
        echo "  1. 网络连接问题"
        echo "  2. 防火墙阻止了外部连接"
        echo "  3. VPN配置问题"
    fi
    
    log_message $LOG_INFO "IP地址检测完成"
}

# 获取外部IP地址
get_external_ip() {
    local service_url="$1"
    local timeout=10
    
    if command -v curl >/dev/null 2>&1; then
        local result=$(curl -s --max-time $timeout "$service_url" 2>/dev/null)
        if [[ $? -eq 0 && -n "$result" ]]; then
            # 提取IP地址
            echo "$result" | grep -oE '[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}' | head -n1
            return 0
        fi
    elif command -v wget >/dev/null 2>&1; then
        local result=$(wget -qO- --timeout=$timeout "$service_url" 2>/dev/null)
        if [[ $? -eq 0 && -n "$result" ]]; then
            echo "$result" | grep -oE '[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}' | head -n1
            return 0
        fi
    fi
    
    return 1
}

# 分析IP检测结果
analyze_ip_results() {
    local ips=("$@")
    
    if [[ ${#ips[@]} -eq 0 ]]; then
        return 1
    fi
    
    # 统计IP地址出现次数
    declare -A ip_count
    for ip in "${ips[@]}"; do
        ((ip_count["$ip"]++))
    done
    
    echo -e "${CYAN}=== IP检测结果分析 ===${NC}"
    
    # 显示所有检测到的IP
    for ip in "${!ip_count[@]}"; do
        local count=${ip_count["$ip"]}
        echo "  IP: $ip (检测到 $count 次)"
        
        # 分析IP地理位置信息
        analyze_ip_location "$ip"
    done
    
    # 检查IP一致性
    if [[ ${#ip_count[@]} -eq 1 ]]; then
        echo ""
        echo -e "${GREEN}✓ 所有服务检测到相同的IP地址，结果一致${NC}"
    else
        echo ""
        echo -e "${YELLOW}⚠ 检测到不同的IP地址，可能存在问题${NC}"
        echo -e "${YELLOW}  这可能表明网络配置不稳定或存在泄漏${NC}"
    fi
}

# 分析IP地理位置
analyze_ip_location() {
    local ip="$1"
    
    # 简单的IP地址分析
    local first_octet=$(echo "$ip" | cut -d. -f1)
    
    # 检查是否为私有IP
    if [[ "$first_octet" -eq 10 ]] || 
       [[ "$first_octet" -eq 172 && $(echo "$ip" | cut -d. -f2) -ge 16 && $(echo "$ip" | cut -d. -f2) -le 31 ]] ||
       [[ "$first_octet" -eq 192 && $(echo "$ip" | cut -d. -f2) -eq 168 ]]; then
        echo "    类型: 私有IP地址 (可能存在配置问题)"
        return
    fi
    
    # 尝试获取地理位置信息
    if command -v curl >/dev/null 2>&1; then
        local geo_info=$(curl -s --max-time 5 "http://ip-api.com/line/$ip?fields=country,regionName,city,isp" 2>/dev/null)
        if [[ $? -eq 0 && -n "$geo_info" ]]; then
            local country=$(echo "$geo_info" | sed -n '1p')
            local region=$(echo "$geo_info" | sed -n '2p')
            local city=$(echo "$geo_info" | sed -n '3p')
            local isp=$(echo "$geo_info" | sed -n '4p')
            
            if [[ -n "$country" && "$country" != "fail" ]]; then
                echo "    位置: $country, $region, $city"
                echo "    ISP: $isp"
            fi
        fi
    fi
}

# 完整安全检测
comprehensive_security_check() {
    echo -e "${BLUE}完整安全检测${NC}"
    echo ""
    
    log_message $LOG_INFO "开始完整安全检测"
    
    echo -e "${CYAN}=== 1. VPN连接状态检查 ===${NC}"
    check_vpn_connection_status
    echo ""
    
    echo -e "${CYAN}=== 2. IP地址泄漏检测 ===${NC}"
    check_ip_address
    echo ""
    
    echo -e "${CYAN}=== 3. DNS泄漏检测 ===${NC}"
    source "$MODULES_DIR/security_core.sh"
    check_dns_leak
    echo ""
    
    echo -e "${CYAN}=== 4. IPv6泄漏检测 ===${NC}"
    check_ipv6_leak
    echo ""
    
    echo -e "${CYAN}=== 5. WebRTC泄漏检测 ===${NC}"
    check_webrtc_leak
    echo ""
    
    echo -e "${CYAN}=== 6. 网络延迟测试 ===${NC}"
    test_network_latency
    echo ""
    
    echo -e "${GREEN}=== 安全检测完成 ===${NC}"
    log_message $LOG_INFO "完整安全检测完成"
}

# 检查VPN连接状态
check_vpn_connection_status() {
    echo -e "${YELLOW}检查VPN连接状态...${NC}"

    # 检查OpenVPN进程（包括Tunnelblick）
    if pgrep -f openvpn >/dev/null; then
        echo -e "${GREEN}✓ OpenVPN进程正在运行${NC}"

        # 检查VPN接口
        local vpn_interface
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS使用utun接口
            vpn_interface=$(ifconfig | grep -E "utun[0-9]+.*inet " | head -n1 | awk '{print $1}' | sed 's/://')
            if [[ -n "$vpn_interface" ]]; then
                echo -e "${GREEN}✓ VPN接口已创建: $vpn_interface${NC}"

                # 检查接口IP地址
                local vpn_ip=$(ifconfig "$vpn_interface" | grep "inet " | awk '{print $2}')
                if [[ -n "$vpn_ip" ]]; then
                    echo -e "${GREEN}✓ VPN接口IP地址: $vpn_ip${NC}"
                else
                    echo -e "${YELLOW}⚠ VPN接口未分配IP地址${NC}"
                fi
            else
                echo -e "${RED}✗ 未找到VPN接口${NC}"
            fi
        else
            # Linux使用tun/tap接口
            vpn_interface=$(ip link show | grep -E "tun[0-9]+|tap[0-9]+" | head -n1 | awk -F: '{print $2}' | tr -d ' ')
            if [[ -n "$vpn_interface" ]]; then
                echo -e "${GREEN}✓ VPN接口已创建: $vpn_interface${NC}"

                # 检查接口状态
                local interface_status=$(ip link show "$vpn_interface" | grep "state UP")
                if [[ -n "$interface_status" ]]; then
                    echo -e "${GREEN}✓ VPN接口状态正常${NC}"
                else
                    echo -e "${YELLOW}⚠ VPN接口未激活${NC}"
                fi
            else
                echo -e "${RED}✗ 未找到VPN接口${NC}"
            fi
        fi
    else
        echo -e "${RED}✗ OpenVPN进程未运行${NC}"
    fi
}

# 检查IPv6泄漏
check_ipv6_leak() {
    echo -e "${YELLOW}检查IPv6泄漏...${NC}"
    
    # 检查IPv6是否被禁用
    if [[ -f "/proc/sys/net/ipv6/conf/all/disable_ipv6" ]]; then
        local ipv6_disabled=$(cat /proc/sys/net/ipv6/conf/all/disable_ipv6)
        if [[ "$ipv6_disabled" == "1" ]]; then
            echo -e "${GREEN}✓ IPv6已被禁用${NC}"
        else
            echo -e "${YELLOW}⚠ IPv6未被禁用，可能存在泄漏风险${NC}"
            
            # 尝试获取IPv6地址
            if command -v curl >/dev/null 2>&1; then
                local ipv6_addr=$(curl -6 -s --max-time 5 "https://ipv6.icanhazip.com" 2>/dev/null)
                if [[ -n "$ipv6_addr" ]]; then
                    echo -e "${RED}✗ 检测到IPv6地址: $ipv6_addr${NC}"
                    echo -e "${RED}  这表明存在IPv6泄漏！${NC}"
                else
                    echo -e "${GREEN}✓ 未检测到IPv6连接${NC}"
                fi
            fi
        fi
    else
        echo -e "${YELLOW}⚠ 无法检查IPv6状态${NC}"
    fi
}

# 检查WebRTC泄漏
check_webrtc_leak() {
    echo -e "${YELLOW}检查WebRTC泄漏...${NC}"
    echo -e "${BLUE}注意: WebRTC泄漏主要影响浏览器，建议在浏览器中禁用WebRTC${NC}"
    echo -e "${BLUE}Firefox: 在about:config中设置media.peerconnection.enabled为false${NC}"
    echo -e "${BLUE}Chrome: 安装WebRTC Leak Prevent扩展${NC}"
}
