#!/bin/bash

# OpenVPN核心功能模块
# 提供OpenVPN配置、连接、管理等核心功能

# 如果变量未定义，设置默认值
if [[ -z "${CONFIGS_DIR:-}" ]]; then
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
    CONFIGS_DIR="$SCRIPT_DIR/configs"
    LOGS_DIR="$SCRIPT_DIR/logs"
    USER_CONFIG_DIR="$HOME/.vpn-security-client"

    # 颜色定义
    RED='\033[0;31m'
    GREEN='\033[0;32m'
    YELLOW='\033[1;33m'
    BLUE='\033[0;34m'
    NC='\033[0m'

    # 日志级别
    LOG_ERROR=1
    LOG_WARN=2
    LOG_INFO=3
    LOG_DEBUG=4

    # 简单日志函数
    log_message() {
        local level=$1
        local message=$2
        local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
        echo "[$timestamp] [LEVEL:$level] $message" >> "$LOGS_DIR/vpn-client.log"
    }
fi

# 确保目录存在
mkdir -p "$USER_CONFIG_DIR" "$CONFIGS_DIR" "$LOGS_DIR" 2>/dev/null || true

# OpenVPN相关路径
OPENVPN_CONFIG_DIR="$CONFIGS_DIR/openvpn"
OPENVPN_LOG_FILE="$LOGS_DIR/openvpn.log"
OPENVPN_PID_FILE="$USER_CONFIG_DIR/openvpn.pid"
OPENVPN_STATUS_FILE="$USER_CONFIG_DIR/openvpn.status"

# 创建OpenVPN配置目录
mkdir -p "$OPENVPN_CONFIG_DIR" 2>/dev/null || true

# 检查OpenVPN是否安装
check_openvpn_installation() {
    log_message $LOG_INFO "检查OpenVPN安装状态"
    
    if ! command -v openvpn >/dev/null 2>&1; then
        log_message $LOG_ERROR "OpenVPN未安装"
        echo -e "${RED}错误: OpenVPN未安装${NC}"
        echo -e "${YELLOW}请运行以下命令安装OpenVPN:${NC}"
        echo "  Ubuntu/Debian: sudo apt-get install openvpn"
        echo "  CentOS/RHEL: sudo yum install openvpn"
        echo "  Fedora: sudo dnf install openvpn"
        return 1
    fi
    
    local version=$(openvpn --version | head -n1 | awk '{print $2}')
    log_message $LOG_INFO "OpenVPN版本: $version"
    echo -e "${GREEN}OpenVPN已安装 (版本: $version)${NC}"
    return 0
}

# 配置OpenVPN连接
configure_openvpn() {
    echo -e "${BLUE}配置OpenVPN连接${NC}"
    echo ""
    
    echo "请选择配置方式:"
    echo "1. 导入.ovpn配置文件"
    echo "2. 手动输入服务器信息"
    echo "3. 从URL下载配置文件"
    
    read -p "请选择 [1-3]: " config_method
    
    case $config_method in
        1) import_ovpn_file ;;
        2) manual_openvpn_config ;;
        3) download_ovpn_config ;;
        *) 
            echo -e "${RED}无效选项${NC}"
            return 1
            ;;
    esac
}

# 导入.ovpn配置文件
import_ovpn_file() {
    echo -e "${BLUE}导入OpenVPN配置文件${NC}"
    echo ""
    
    read -p "请输入.ovpn文件的完整路径: " ovpn_path
    
    if [[ ! -f "$ovpn_path" ]]; then
        echo -e "${RED}错误: 文件不存在: $ovpn_path${NC}"
        return 1
    fi
    
    # 验证配置文件格式
    if ! validate_ovpn_file "$ovpn_path"; then
        echo -e "${RED}错误: 无效的OpenVPN配置文件${NC}"
        return 1
    fi
    
    # 生成配置名称
    local config_name
    read -p "请输入配置名称 (默认: $(basename "$ovpn_path" .ovpn)): " config_name
    config_name=${config_name:-$(basename "$ovpn_path" .ovpn)}
    
    local target_config="$OPENVPN_CONFIG_DIR/${config_name}.ovpn"
    
    # 复制并处理配置文件
    if process_ovpn_config "$ovpn_path" "$target_config"; then
        echo -e "${GREEN}配置文件已成功导入: $target_config${NC}"
        log_message $LOG_INFO "OpenVPN配置已导入: $config_name"
        
        # 设置为默认配置
        echo "$config_name" > "$USER_CONFIG_DIR/default_config"
        echo -e "${GREEN}已设置为默认配置${NC}"
        
        return 0
    else
        echo -e "${RED}配置文件导入失败${NC}"
        return 1
    fi
}

# 验证OpenVPN配置文件
validate_ovpn_file() {
    local file_path="$1"
    
    # 检查必要的配置项
    local required_items=("remote" "dev")
    local missing_items=()
    
    for item in "${required_items[@]}"; do
        if ! grep -q "^$item " "$file_path"; then
            missing_items+=("$item")
        fi
    done
    
    if [[ ${#missing_items[@]} -gt 0 ]]; then
        echo -e "${RED}配置文件缺少必要项: ${missing_items[*]}${NC}"
        return 1
    fi
    
    # 检查文件格式
    if ! grep -q "client" "$file_path" && ! grep -q "remote" "$file_path"; then
        echo -e "${RED}这不是一个有效的OpenVPN客户端配置文件${NC}"
        return 1
    fi
    
    return 0
}

# 处理OpenVPN配置文件
process_ovpn_config() {
    local source_file="$1"
    local target_file="$2"
    
    log_message $LOG_INFO "处理OpenVPN配置文件: $source_file -> $target_file"
    
    # 复制原始文件
    cp "$source_file" "$target_file" || return 1
    
    # 添加安全增强配置 - 移除不兼容的选项
    cat >> "$target_file" << 'EOF'

# 安全增强配置 - 由VPN安全客户端添加
script-security 2
dhcp-option DNS *******
dhcp-option DNS *******
redirect-gateway def1
route-delay 2
EOF
    
    # 创建状态文件路径配置
    echo "status $OPENVPN_STATUS_FILE" >> "$target_file"
    echo "log-append $OPENVPN_LOG_FILE" >> "$target_file"
    
    return 0
}

# 手动配置OpenVPN
manual_openvpn_config() {
    echo -e "${BLUE}手动配置OpenVPN${NC}"
    echo ""
    
    read -p "请输入VPN服务器地址: " server_host
    read -p "请输入端口 (默认: 1194): " server_port
    server_port=${server_port:-1194}
    
    read -p "请输入协议 (udp/tcp, 默认: udp): " protocol
    protocol=${protocol:-udp}
    
    read -p "请输入配置名称: " config_name
    
    if [[ -z "$config_name" ]]; then
        echo -e "${RED}配置名称不能为空${NC}"
        return 1
    fi
    
    local config_file="$OPENVPN_CONFIG_DIR/${config_name}.ovpn"
    
    # 生成基础配置文件
    cat > "$config_file" << EOF
client
dev tun
proto $protocol
remote $server_host $server_port
resolv-retry infinite
nobind
persist-key
persist-tun
ca ca.crt
cert client.crt
key client.key
remote-cert-tls server
cipher AES-256-CBC
verb 3

# 安全增强配置
script-security 2
up /etc/openvpn/update-resolv-conf
down /etc/openvpn/update-resolv-conf
dhcp-option DNS *******
dhcp-option DNS *******
block-outside-dns
redirect-gateway def1
route-delay 2
status $OPENVPN_STATUS_FILE
log-append $OPENVPN_LOG_FILE
EOF
    
    echo -e "${GREEN}基础配置文件已创建: $config_file${NC}"
    echo -e "${YELLOW}请将证书文件 (ca.crt, client.crt, client.key) 放置在同一目录下${NC}"
    
    # 设置为默认配置
    echo "$config_name" > "$USER_CONFIG_DIR/default_config"
    
    log_message $LOG_INFO "手动OpenVPN配置已创建: $config_name"
    return 0
}

# 从URL下载配置文件
download_ovpn_config() {
    echo -e "${BLUE}从URL下载OpenVPN配置${NC}"
    echo ""
    
    read -p "请输入配置文件URL: " config_url
    
    if [[ -z "$config_url" ]]; then
        echo -e "${RED}URL不能为空${NC}"
        return 1
    fi
    
    read -p "请输入配置名称: " config_name
    if [[ -z "$config_name" ]]; then
        config_name="downloaded_$(date +%Y%m%d_%H%M%S)"
    fi
    
    local temp_file="/tmp/openvpn_download.ovpn"
    local target_file="$OPENVPN_CONFIG_DIR/${config_name}.ovpn"
    
    echo -e "${BLUE}正在下载配置文件...${NC}"
    
    if command -v curl >/dev/null 2>&1; then
        curl -L -o "$temp_file" "$config_url" || {
            echo -e "${RED}下载失败${NC}"
            return 1
        }
    elif command -v wget >/dev/null 2>&1; then
        wget -O "$temp_file" "$config_url" || {
            echo -e "${RED}下载失败${NC}"
            return 1
        }
    else
        echo -e "${RED}错误: 需要curl或wget来下载文件${NC}"
        return 1
    fi
    
    # 验证下载的文件
    if validate_ovpn_file "$temp_file"; then
        if process_ovpn_config "$temp_file" "$target_file"; then
            echo -e "${GREEN}配置文件下载并处理成功${NC}"
            echo "$config_name" > "$USER_CONFIG_DIR/default_config"
            rm -f "$temp_file"
            log_message $LOG_INFO "OpenVPN配置已从URL下载: $config_name"
            return 0
        fi
    fi
    
    echo -e "${RED}配置文件下载或处理失败${NC}"
    rm -f "$temp_file"
    return 1
}

# 列出可用的配置
list_openvpn_configs() {
    echo -e "${BLUE}可用的OpenVPN配置:${NC}"
    echo ""
    
    local configs=($(ls "$OPENVPN_CONFIG_DIR"/*.ovpn 2>/dev/null | xargs -n1 basename | sed 's/\.ovpn$//' || true))
    
    if [[ ${#configs[@]} -eq 0 ]]; then
        echo -e "${YELLOW}没有找到OpenVPN配置文件${NC}"
        return 1
    fi
    
    local default_config=""
    if [[ -f "$USER_CONFIG_DIR/default_config" ]]; then
        default_config=$(cat "$USER_CONFIG_DIR/default_config")
    fi
    
    for i in "${!configs[@]}"; do
        local config="${configs[$i]}"
        if [[ "$config" == "$default_config" ]]; then
            echo -e "${GREEN}$((i+1)). $config (默认)${NC}"
        else
            echo -e "$((i+1)). $config"
        fi
    done
    
    return 0
}
