#!/bin/bash

# 安全防护核心模块
# 提供Kill Switch、DNS防护、IPv6禁用等防IP泄漏功能

# 如果变量未定义，设置默认值
if [[ -z "${USER_CONFIG_DIR:-}" ]]; then
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
    CONFIGS_DIR="$SCRIPT_DIR/configs"
    LOGS_DIR="$SCRIPT_DIR/logs"
    BACKUP_DIR="$SCRIPT_DIR/backup"
    USER_CONFIG_DIR="$HOME/.vpn-security-client"
    OPENVPN_CONFIG_DIR="$CONFIGS_DIR/openvpn"

    # 颜色定义
    RED='\033[0;31m'
    GREEN='\033[0;32m'
    YELLOW='\033[1;33m'
    BLUE='\033[0;34m'
    NC='\033[0m'

    # 日志级别
    LOG_ERROR=1
    LOG_WARN=2
    LOG_INFO=3
    LOG_DEBUG=4

    # 简单日志函数
    log_message() {
        local level=$1
        local message=$2
        local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
        echo "[$timestamp] [LEVEL:$level] $message" >> "$LOGS_DIR/vpn-client.log"
    }

    # 获取VPN服务器IP函数
    get_vpn_server_ip() {
        local default_config_file

        if [[ -f "$USER_CONFIG_DIR/default_config" ]]; then
            local config_name=$(cat "$USER_CONFIG_DIR/default_config")
            default_config_file="$OPENVPN_CONFIG_DIR/${config_name}.ovpn"
        fi

        if [[ -f "$default_config_file" ]]; then
            grep "^remote " "$default_config_file" | head -n1 | awk '{print $2}'
        fi
    }
fi

# 确保目录存在
mkdir -p "$USER_CONFIG_DIR" "$BACKUP_DIR" "$LOGS_DIR" 2>/dev/null || true

# 安全配置文件
SECURITY_CONFIG_FILE="$USER_CONFIG_DIR/security.conf"
FIREWALL_BACKUP_FILE="$BACKUP_DIR/iptables_backup.rules"
DNS_BACKUP_FILE="$BACKUP_DIR/resolv.conf.backup"
ROUTES_BACKUP_FILE="$BACKUP_DIR/routes.backup"

# 安全状态文件
KILL_SWITCH_STATUS_FILE="$USER_CONFIG_DIR/kill_switch.status"
DNS_PROTECTION_STATUS_FILE="$USER_CONFIG_DIR/dns_protection.status"

# 初始化安全模块
init_security_module() {
    log_message $LOG_INFO "初始化安全防护模块"
    
    # 创建安全配置文件
    if [[ ! -f "$SECURITY_CONFIG_FILE" ]]; then
        cat > "$SECURITY_CONFIG_FILE" << 'EOF'
# VPN安全客户端配置文件
KILL_SWITCH_ENABLED=false
DNS_PROTECTION_ENABLED=false
IPV6_DISABLED=false
LEAK_PROTECTION_ENABLED=false
ALLOWED_VPN_INTERFACE=""
VPN_SERVER_IP=""
EOF
    fi
    
    # 检查当前安全状态
    check_security_status
}

# 检查安全状态
check_security_status() {
    # 检查Kill Switch状态
    if [[ -f "$KILL_SWITCH_STATUS_FILE" ]]; then
        KILL_SWITCH_ACTIVE=true
    else
        KILL_SWITCH_ACTIVE=false
    fi
    
    # 检查DNS保护状态
    if [[ -f "$DNS_PROTECTION_STATUS_FILE" ]]; then
        DNS_PROTECTION_ACTIVE=true
    else
        DNS_PROTECTION_ACTIVE=false
    fi
}

# 启用Kill Switch (防断网泄漏)
enable_kill_switch() {
    echo -e "${BLUE}启用Kill Switch防护${NC}"
    echo ""
    
    if $KILL_SWITCH_ACTIVE; then
        echo -e "${YELLOW}Kill Switch已经启用${NC}"
        return 0
    fi
    
    echo -e "${YELLOW}警告: Kill Switch将阻止所有非VPN流量${NC}"
    echo -e "${YELLOW}如果VPN断开，您将无法访问互联网直到重新连接VPN或禁用Kill Switch${NC}"
    echo ""
    
    read -p "确认启用Kill Switch? (y/N): " confirm
    
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}操作已取消${NC}"
        return 0
    fi
    
    log_message $LOG_INFO "启用Kill Switch防护"
    
    # 备份当前防火墙规则
    backup_firewall_rules
    
    # 获取VPN服务器IP
    local vpn_server_ip
    vpn_server_ip=$(get_vpn_server_ip)
    
    if [[ -z "$vpn_server_ip" ]]; then
        echo -e "${RED}错误: 无法获取VPN服务器IP地址${NC}"
        echo -e "${YELLOW}请先配置并连接VPN${NC}"
        return 1
    fi
    
    # 设置Kill Switch规则
    if setup_kill_switch_rules "$vpn_server_ip"; then
        touch "$KILL_SWITCH_STATUS_FILE"
        KILL_SWITCH_ACTIVE=true
        echo -e "${GREEN}Kill Switch已成功启用${NC}"
        log_message $LOG_INFO "Kill Switch防护已启用"
        return 0
    else
        echo -e "${RED}Kill Switch启用失败${NC}"
        return 1
    fi
}

# 禁用Kill Switch
disable_kill_switch() {
    echo -e "${BLUE}禁用Kill Switch防护${NC}"
    echo ""
    
    if ! $KILL_SWITCH_ACTIVE; then
        echo -e "${YELLOW}Kill Switch未启用${NC}"
        return 0
    fi
    
    log_message $LOG_INFO "禁用Kill Switch防护"
    
    # 恢复防火墙规则
    if restore_firewall_rules; then
        rm -f "$KILL_SWITCH_STATUS_FILE"
        KILL_SWITCH_ACTIVE=false
        echo -e "${GREEN}Kill Switch已禁用${NC}"
        log_message $LOG_INFO "Kill Switch防护已禁用"
        return 0
    else
        echo -e "${RED}Kill Switch禁用失败${NC}"
        return 1
    fi
}

# 设置Kill Switch防火墙规则
setup_kill_switch_rules() {
    local vpn_server_ip="$1"
    
    log_message $LOG_INFO "设置Kill Switch防火墙规则"
    
    # 检查是否有root权限
    if [[ $EUID -ne 0 ]]; then
        echo -e "${RED}错误: 需要root权限来设置防火墙规则${NC}"
        echo -e "${YELLOW}请使用sudo运行此脚本${NC}"
        return 1
    fi
    
    # 清除现有规则
    iptables -F OUTPUT 2>/dev/null || true
    iptables -F INPUT 2>/dev/null || true
    
    # 允许本地回环
    iptables -A OUTPUT -o lo -j ACCEPT
    iptables -A INPUT -i lo -j ACCEPT
    
    # 允许已建立的连接
    iptables -A OUTPUT -m state --state ESTABLISHED,RELATED -j ACCEPT
    iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT
    
    # 允许连接到VPN服务器
    iptables -A OUTPUT -d "$vpn_server_ip" -j ACCEPT
    
    # 允许VPN接口流量
    local vpn_interface=$(get_vpn_interface)
    if [[ -n "$vpn_interface" ]]; then
        iptables -A OUTPUT -o "$vpn_interface" -j ACCEPT
        iptables -A INPUT -i "$vpn_interface" -j ACCEPT
    fi
    
    # 允许局域网流量
    iptables -A OUTPUT -d ***********/16 -j ACCEPT
    iptables -A OUTPUT -d 10.0.0.0/8 -j ACCEPT
    iptables -A OUTPUT -d **********/12 -j ACCEPT
    
    # 阻止所有其他出站流量
    iptables -A OUTPUT -j DROP
    
    # 禁用IPv6 (防止IPv6泄漏)
    if command -v ip6tables >/dev/null 2>&1; then
        ip6tables -P INPUT DROP 2>/dev/null || true
        ip6tables -P OUTPUT DROP 2>/dev/null || true
        ip6tables -P FORWARD DROP 2>/dev/null || true
    fi
    
    log_message $LOG_INFO "Kill Switch防火墙规则已设置"
    return 0
}

# 备份防火墙规则
backup_firewall_rules() {
    log_message $LOG_INFO "备份当前防火墙规则"
    
    if command -v iptables-save >/dev/null 2>&1; then
        iptables-save > "$FIREWALL_BACKUP_FILE" 2>/dev/null || {
            log_message $LOG_WARN "无法备份iptables规则"
            return 1
        }
    fi
    
    # 备份路由表
    ip route show > "$ROUTES_BACKUP_FILE" 2>/dev/null || {
        log_message $LOG_WARN "无法备份路由表"
    }
    
    return 0
}

# 恢复防火墙规则
restore_firewall_rules() {
    log_message $LOG_INFO "恢复防火墙规则"
    
    if [[ $EUID -ne 0 ]]; then
        echo -e "${RED}错误: 需要root权限来恢复防火墙规则${NC}"
        return 1
    fi
    
    if [[ -f "$FIREWALL_BACKUP_FILE" ]] && command -v iptables-restore >/dev/null 2>&1; then
        iptables-restore < "$FIREWALL_BACKUP_FILE" 2>/dev/null || {
            log_message $LOG_ERROR "无法恢复iptables规则"
            # 如果恢复失败，清除所有规则
            iptables -F 2>/dev/null || true
            iptables -X 2>/dev/null || true
            iptables -t nat -F 2>/dev/null || true
            iptables -t nat -X 2>/dev/null || true
        }
    else
        # 如果没有备份文件，清除所有规则并设置默认策略
        iptables -F 2>/dev/null || true
        iptables -X 2>/dev/null || true
        iptables -t nat -F 2>/dev/null || true
        iptables -t nat -X 2>/dev/null || true
        iptables -P INPUT ACCEPT 2>/dev/null || true
        iptables -P OUTPUT ACCEPT 2>/dev/null || true
        iptables -P FORWARD ACCEPT 2>/dev/null || true
    fi
    
    # 恢复IPv6
    if command -v ip6tables >/dev/null 2>&1; then
        ip6tables -P INPUT ACCEPT 2>/dev/null || true
        ip6tables -P OUTPUT ACCEPT 2>/dev/null || true
        ip6tables -P FORWARD ACCEPT 2>/dev/null || true
    fi
    
    return 0
}

# 获取VPN服务器IP
get_vpn_server_ip() {
    local default_config_file
    
    if [[ -f "$USER_CONFIG_DIR/default_config" ]]; then
        local config_name=$(cat "$USER_CONFIG_DIR/default_config")
        default_config_file="$OPENVPN_CONFIG_DIR/${config_name}.ovpn"
    fi
    
    if [[ -f "$default_config_file" ]]; then
        grep "^remote " "$default_config_file" | head -n1 | awk '{print $2}'
    fi
}

# 获取VPN接口
get_vpn_interface() {
    # 查找tun或tap接口
    ip link show | grep -E "tun[0-9]+|tap[0-9]+" | head -n1 | awk -F: '{print $2}' | tr -d ' '
}

# 启用DNS保护
enable_dns_protection() {
    echo -e "${BLUE}启用DNS泄漏保护${NC}"
    echo ""
    
    log_message $LOG_INFO "启用DNS保护"
    
    # 备份当前DNS配置
    if [[ -f "/etc/resolv.conf" ]]; then
        cp "/etc/resolv.conf" "$DNS_BACKUP_FILE"
    fi
    
    # 设置安全DNS服务器
    cat > "/etc/resolv.conf" << 'EOF'
# VPN安全客户端DNS配置
nameserver *******
nameserver *******
nameserver *******
nameserver *******
EOF
    
    # 防止DNS配置被覆盖
    chattr +i "/etc/resolv.conf" 2>/dev/null || true
    
    touch "$DNS_PROTECTION_STATUS_FILE"
    echo -e "${GREEN}DNS保护已启用${NC}"
    log_message $LOG_INFO "DNS保护已启用"
}

# 禁用DNS保护
disable_dns_protection() {
    echo -e "${BLUE}禁用DNS泄漏保护${NC}"
    echo ""
    
    log_message $LOG_INFO "禁用DNS保护"
    
    # 移除DNS配置保护
    chattr -i "/etc/resolv.conf" 2>/dev/null || true
    
    # 恢复原始DNS配置
    if [[ -f "$DNS_BACKUP_FILE" ]]; then
        cp "$DNS_BACKUP_FILE" "/etc/resolv.conf"
    fi
    
    rm -f "$DNS_PROTECTION_STATUS_FILE"
    echo -e "${GREEN}DNS保护已禁用${NC}"
    log_message $LOG_INFO "DNS保护已禁用"
}

# 检测DNS泄漏
check_dns_leak() {
    echo -e "${BLUE}检测DNS泄漏${NC}"
    echo ""
    
    log_message $LOG_INFO "开始DNS泄漏检测"
    
    # 检查当前使用的DNS服务器
    echo -e "${YELLOW}当前DNS配置:${NC}"
    cat /etc/resolv.conf | grep nameserver
    echo ""
    
    # 使用多个测试域名检查DNS解析
    local test_domains=("whoami.akamai.net" "myip.opendns.com" "icanhazip.com")
    
    echo -e "${YELLOW}DNS解析测试:${NC}"
    for domain in "${test_domains[@]}"; do
        echo -n "测试 $domain: "
        local result=$(dig +short "$domain" 2>/dev/null | tail -n1)
        if [[ -n "$result" ]]; then
            echo -e "${GREEN}$result${NC}"
        else
            echo -e "${RED}解析失败${NC}"
        fi
    done
    
    echo ""
    echo -e "${BLUE}建议: 如果上述IP地址与您的真实IP相同，可能存在DNS泄漏${NC}"
    
    log_message $LOG_INFO "DNS泄漏检测完成"
}
