#!/bin/bash

# Linux VPN安全客户端安装脚本
# 自动检测系统并安装必要依赖

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 显示标题
show_header() {
    clear
    echo -e "${BLUE}================================================================${NC}"
    echo -e "${BLUE}           Linux VPN安全客户端 - 安装程序${NC}"
    echo -e "${BLUE}================================================================${NC}"
    echo ""
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        source /etc/os-release
        OS=$ID
        VERSION=$VERSION_ID
    elif [[ -f /etc/redhat-release ]]; then
        OS="centos"
    elif [[ -f /etc/debian_version ]]; then
        OS="debian"
    else
        echo -e "${RED}无法检测操作系统${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}检测到操作系统: $OS $VERSION${NC}"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        echo -e "${RED}错误: 需要root权限来安装依赖${NC}"
        echo -e "${YELLOW}请使用sudo运行此脚本: sudo $0${NC}"
        exit 1
    fi
}

# 更新包管理器
update_package_manager() {
    echo -e "${BLUE}更新包管理器...${NC}"
    
    case $OS in
        ubuntu|debian)
            apt-get update -y
            ;;
        centos|rhel)
            yum update -y
            ;;
        fedora)
            dnf update -y
            ;;
        *)
            echo -e "${YELLOW}未知的包管理器，跳过更新${NC}"
            ;;
    esac
}

# 安装依赖包
install_dependencies() {
    echo -e "${BLUE}安装系统依赖...${NC}"
    
    local packages=""
    
    case $OS in
        ubuntu|debian)
            packages="openvpn iptables iproute2 dnsutils curl wget net-tools"
            apt-get install -y $packages
            ;;
        centos|rhel)
            # 启用EPEL仓库
            if ! rpm -qa | grep -q epel-release; then
                yum install -y epel-release
            fi
            packages="openvpn iptables iproute bind-utils curl wget net-tools"
            yum install -y $packages
            ;;
        fedora)
            packages="openvpn iptables iproute bind-utils curl wget net-tools"
            dnf install -y $packages
            ;;
        *)
            echo -e "${RED}不支持的操作系统: $OS${NC}"
            echo -e "${YELLOW}请手动安装以下依赖:${NC}"
            echo "  - openvpn"
            echo "  - iptables"
            echo "  - iproute2/iproute"
            echo "  - dnsutils/bind-utils"
            echo "  - curl"
            echo "  - wget"
            return 1
            ;;
    esac
    
    echo -e "${GREEN}依赖安装完成${NC}"
}

# 验证安装
verify_installation() {
    echo -e "${BLUE}验证安装...${NC}"
    
    local required_commands=("openvpn" "iptables" "ip" "dig" "curl")
    local missing_commands=()
    
    for cmd in "${required_commands[@]}"; do
        if command -v "$cmd" >/dev/null 2>&1; then
            echo -e "${GREEN}✓ $cmd${NC}"
        else
            echo -e "${RED}✗ $cmd${NC}"
            missing_commands+=("$cmd")
        fi
    done
    
    if [[ ${#missing_commands[@]} -gt 0 ]]; then
        echo -e "${RED}以下命令缺失: ${missing_commands[*]}${NC}"
        return 1
    fi
    
    echo -e "${GREEN}所有依赖验证通过${NC}"
    return 0
}

# 设置权限
setup_permissions() {
    echo -e "${BLUE}设置文件权限...${NC}"
    
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    # 设置主脚本权限
    chmod +x "$script_dir/server_proxy.sh"
    
    # 设置模块权限
    if [[ -d "$script_dir/modules" ]]; then
        chmod +x "$script_dir/modules"/*.sh
    fi
    
    # 创建必要目录
    mkdir -p "$script_dir"/{configs,logs,backup}
    mkdir -p "$script_dir/configs/openvpn"
    
    echo -e "${GREEN}权限设置完成${NC}"
}

# 创建系统服务 (可选)
create_systemd_service() {
    echo ""
    read -p "是否创建systemd服务以便系统启动时自动运行? (y/N): " create_service
    
    if [[ $create_service =~ ^[Yy]$ ]]; then
        local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
        local service_file="/etc/systemd/system/vpn-security-client.service"
        
        cat > "$service_file" << EOF
[Unit]
Description=VPN Security Client
After=network.target

[Service]
Type=forking
ExecStart=$script_dir/server_proxy.sh --daemon
ExecStop=/bin/kill -TERM \$MAINPID
PIDFile=/var/run/vpn-security-client.pid
Restart=always
User=root

[Install]
WantedBy=multi-user.target
EOF
        
        systemctl daemon-reload
        echo -e "${GREEN}systemd服务已创建${NC}"
        echo -e "${YELLOW}使用以下命令管理服务:${NC}"
        echo "  启动: sudo systemctl start vpn-security-client"
        echo "  停止: sudo systemctl stop vpn-security-client"
        echo "  开机启动: sudo systemctl enable vpn-security-client"
    fi
}

# 显示使用说明
show_usage_info() {
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    echo ""
    echo -e "${GREEN}================================================================${NC}"
    echo -e "${GREEN}                    安装完成！${NC}"
    echo -e "${GREEN}================================================================${NC}"
    echo ""
    echo -e "${YELLOW}使用方法:${NC}"
    echo "  1. 运行主程序: sudo $script_dir/server_proxy.sh"
    echo "  2. 首次使用请先配置OpenVPN连接 (选项1)"
    echo "  3. 连接VPN (选项2)"
    echo "  4. 启用Kill Switch防护 (选项5)"
    echo "  5. 可选择启用全局代理模式 (选项8)"
    echo ""
    echo -e "${YELLOW}重要提示:${NC}"
    echo "  - 程序需要root权限运行"
    echo "  - 首次使用前请阅读README.md"
    echo "  - 建议在测试环境中先行验证"
    echo "  - 定期备份VPN配置文件"
    echo ""
    echo -e "${BLUE}文档位置: $script_dir/README.md${NC}"
    echo ""
}

# 主安装流程
main() {
    show_header
    
    echo -e "${BLUE}开始安装Linux VPN安全客户端...${NC}"
    echo ""
    
    # 检测系统
    detect_os
    echo ""
    
    # 检查权限
    check_root
    echo ""
    
    # 更新包管理器
    update_package_manager
    echo ""
    
    # 安装依赖
    if install_dependencies; then
        echo ""
    else
        echo -e "${RED}依赖安装失败${NC}"
        exit 1
    fi
    
    # 验证安装
    if verify_installation; then
        echo ""
    else
        echo -e "${RED}安装验证失败${NC}"
        exit 1
    fi
    
    # 设置权限
    setup_permissions
    echo ""
    
    # 创建系统服务 (可选)
    create_systemd_service
    
    # 显示使用说明
    show_usage_info
}

# 运行主程序
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
