#!/bin/bash

# Linux VPN安全客户端安装脚本
# 自动检测系统并安装必要依赖

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 显示标题
show_header() {
    clear
    echo -e "${BLUE}================================================================${NC}"
    echo -e "${BLUE}           Linux VPN安全客户端 - 安装程序${NC}"
    echo -e "${BLUE}================================================================${NC}"
    echo ""
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        source /etc/os-release
        OS=$ID
        VERSION=$VERSION_ID
    elif [[ -f /etc/redhat-release ]]; then
        OS="centos"
    elif [[ -f /etc/debian_version ]]; then
        OS="debian"
    else
        echo -e "${RED}无法检测操作系统${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}检测到操作系统: $OS $VERSION${NC}"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        echo -e "${RED}错误: 需要root权限来安装依赖${NC}"
        echo -e "${YELLOW}请使用sudo运行此脚本: sudo $0${NC}"
        exit 1
    fi
}

# 更新包管理器
update_package_manager() {
    echo -e "${BLUE}更新包管理器...${NC}"
    
    case $OS in
        ubuntu|debian)
            apt-get update -y
            ;;
        centos|rhel)
            yum update -y
            ;;
        fedora)
            dnf update -y
            ;;
        *)
            echo -e "${YELLOW}未知的包管理器，跳过更新${NC}"
            ;;
    esac
}

# 安装依赖包
install_dependencies() {
    echo -e "${BLUE}安装系统依赖...${NC}"

    local packages=""

    case $OS in
        ubuntu|debian)
            # 先安装基础依赖，但不安装旧版本的OpenVPN
            packages="iptables iproute2 dnsutils curl wget net-tools build-essential libssl-dev liblzo2-dev libpam0g-dev"
            apt-get update
            apt-get install -y $packages
            ;;
        centos|rhel)
            # 启用EPEL仓库
            if ! rpm -qa | grep -q epel-release; then
                yum install -y epel-release
            fi
            packages="iptables iproute bind-utils curl wget net-tools gcc gcc-c++ openssl-devel lzo-devel pam-devel"
            yum install -y $packages
            ;;
        fedora)
            packages="iptables iproute bind-utils curl wget net-tools gcc gcc-c++ openssl-devel lzo-devel pam-devel"
            dnf install -y $packages
            ;;
        *)
            echo -e "${RED}不支持的操作系统: $OS${NC}"
            echo -e "${YELLOW}请手动安装以下依赖:${NC}"
            echo "  - iptables"
            echo "  - iproute2/iproute"
            echo "  - dnsutils/bind-utils"
            echo "  - curl"
            echo "  - wget"
            echo "  - build tools"
            return 1
            ;;
    esac

    echo -e "${GREEN}基础依赖安装完成${NC}"

    # 安装支持block-outside-dns的OpenVPN版本
    install_modern_openvpn
}

# 安装支持block-outside-dns的现代OpenVPN版本
install_modern_openvpn() {
    echo -e "${BLUE}安装现代版本OpenVPN (支持block-outside-dns)...${NC}"

    # 检查是否已安装合适版本
    if command -v openvpn >/dev/null 2>&1; then
        local current_version=$(openvpn --version | head -1 | awk '{print $2}')
        echo "当前OpenVPN版本: $current_version"

        # 检查版本是否支持block-outside-dns (需要2.6.0+)
        if version_compare "$current_version" "2.6.0"; then
            echo -e "${GREEN}✓ 当前版本支持block-outside-dns${NC}"
            return 0
        else
            echo -e "${YELLOW}当前版本不支持block-outside-dns，需要升级${NC}"
        fi
    fi

    case $OS in
        ubuntu|debian)
            install_openvpn_ubuntu
            ;;
        centos|rhel)
            install_openvpn_centos
            ;;
        fedora)
            install_openvpn_fedora
            ;;
        *)
            echo -e "${YELLOW}尝试从源码编译安装...${NC}"
            install_openvpn_from_source
            ;;
    esac
}

# Ubuntu/Debian安装现代OpenVPN
install_openvpn_ubuntu() {
    echo -e "${BLUE}为Ubuntu/Debian安装现代OpenVPN...${NC}"

    # 添加OpenVPN官方仓库
    if [[ ! -f /etc/apt/sources.list.d/openvpn.list ]]; then
        # 安装必要的包
        apt-get install -y software-properties-common apt-transport-https ca-certificates gnupg

        # 添加OpenVPN仓库密钥
        curl -fsSL https://swupdate.openvpn.net/repos/openvpn-repo-pkg-key.pub | gpg --dearmor > /etc/apt/trusted.gpg.d/openvpn.gpg

        # 添加仓库
        echo "deb https://build.openvpn.net/debian/openvpn/stable $(lsb_release -sc) main" > /etc/apt/sources.list.d/openvpn.list

        # 更新包列表
        apt-get update
    fi

    # 安装OpenVPN
    apt-get install -y openvpn

    # 验证安装
    verify_openvpn_installation
}

# CentOS/RHEL安装现代OpenVPN
install_openvpn_centos() {
    echo -e "${BLUE}为CentOS/RHEL安装现代OpenVPN...${NC}"

    # 添加OpenVPN仓库
    if [[ ! -f /etc/yum.repos.d/openvpn.repo ]]; then
        cat > /etc/yum.repos.d/openvpn.repo << 'EOF'
[openvpn]
name=OpenVPN Repository
baseurl=https://build.openvpn.net/RHEL$releasever/$basearch/
enabled=1
gpgcheck=1
gpgkey=https://swupdate.openvpn.net/repos/openvpn-repo-pkg-key.pub
EOF
    fi

    # 安装OpenVPN
    yum install -y openvpn

    # 验证安装
    verify_openvpn_installation
}

# Fedora安装现代OpenVPN
install_openvpn_fedora() {
    echo -e "${BLUE}为Fedora安装现代OpenVPN...${NC}"

    # Fedora通常有较新的OpenVPN版本
    dnf install -y openvpn

    # 验证安装
    verify_openvpn_installation
}

# 从源码编译安装OpenVPN
install_openvpn_from_source() {
    echo -e "${BLUE}从源码编译安装OpenVPN...${NC}"

    local openvpn_version="2.6.12"
    local build_dir="/tmp/openvpn-build"

    # 创建构建目录
    mkdir -p "$build_dir"
    cd "$build_dir"

    # 下载源码
    echo "下载OpenVPN $openvpn_version 源码..."
    wget "https://swupdate.openvpn.org/community/releases/openvpn-${openvpn_version}.tar.gz"
    tar -xzf "openvpn-${openvpn_version}.tar.gz"
    cd "openvpn-${openvpn_version}"

    # 配置编译选项
    echo "配置编译选项..."
    ./configure --enable-iproute2 --enable-plugins --enable-plugin-auth-pam --enable-plugin-down-root

    # 编译
    echo "编译OpenVPN..."
    make -j$(nproc)

    # 安装
    echo "安装OpenVPN..."
    make install

    # 创建符号链接
    ln -sf /usr/local/sbin/openvpn /usr/sbin/openvpn

    # 清理构建目录
    cd /
    rm -rf "$build_dir"

    # 验证安装
    verify_openvpn_installation
}

# 验证OpenVPN安装
verify_openvpn_installation() {
    echo -e "${BLUE}验证OpenVPN安装...${NC}"

    if command -v openvpn >/dev/null 2>&1; then
        local version=$(openvpn --version | head -1 | awk '{print $2}')
        echo -e "${GREEN}✓ OpenVPN已安装，版本: $version${NC}"

        # 检查是否支持block-outside-dns
        if version_compare "$version" "2.6.0"; then
            echo -e "${GREEN}✓ 支持block-outside-dns功能${NC}"
        else
            echo -e "${YELLOW}⚠ 版本可能不支持block-outside-dns${NC}"
        fi

        return 0
    else
        echo -e "${RED}✗ OpenVPN安装失败${NC}"
        return 1
    fi
}

# 版本比较函数
version_compare() {
    local version1="$1"
    local version2="$2"

    # 移除版本号中的非数字字符
    version1=$(echo "$version1" | sed 's/[^0-9.]//g')
    version2=$(echo "$version2" | sed 's/[^0-9.]//g')

    # 使用sort进行版本比较
    if [[ "$(printf '%s\n' "$version1" "$version2" | sort -V | head -n1)" == "$version2" ]]; then
        return 0  # version1 >= version2
    else
        return 1  # version1 < version2
    fi
}

# 验证安装
verify_installation() {
    echo -e "${BLUE}验证安装...${NC}"
    
    local required_commands=("openvpn" "iptables" "ip" "dig" "curl")
    local missing_commands=()
    
    for cmd in "${required_commands[@]}"; do
        if command -v "$cmd" >/dev/null 2>&1; then
            echo -e "${GREEN}✓ $cmd${NC}"
        else
            echo -e "${RED}✗ $cmd${NC}"
            missing_commands+=("$cmd")
        fi
    done
    
    if [[ ${#missing_commands[@]} -gt 0 ]]; then
        echo -e "${RED}以下命令缺失: ${missing_commands[*]}${NC}"
        return 1
    fi
    
    echo -e "${GREEN}所有依赖验证通过${NC}"
    return 0
}

# 设置权限
setup_permissions() {
    echo -e "${BLUE}设置文件权限...${NC}"
    
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    # 设置主脚本权限
    chmod +x "$script_dir/server_proxy.sh"
    
    # 设置模块权限
    if [[ -d "$script_dir/modules" ]]; then
        chmod +x "$script_dir/modules"/*.sh
    fi
    
    # 创建必要目录
    mkdir -p "$script_dir"/{configs,logs,backup}
    mkdir -p "$script_dir/configs/openvpn"
    
    echo -e "${GREEN}权限设置完成${NC}"
}

# 创建系统服务 (可选)
create_systemd_service() {
    echo ""
    read -p "是否创建systemd服务以便系统启动时自动运行? (y/N): " create_service
    
    if [[ $create_service =~ ^[Yy]$ ]]; then
        local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
        local service_file="/etc/systemd/system/vpn-security-client.service"
        
        cat > "$service_file" << EOF
[Unit]
Description=VPN Security Client
After=network.target

[Service]
Type=forking
ExecStart=$script_dir/server_proxy.sh --daemon
ExecStop=/bin/kill -TERM \$MAINPID
PIDFile=/var/run/vpn-security-client.pid
Restart=always
User=root

[Install]
WantedBy=multi-user.target
EOF
        
        systemctl daemon-reload
        echo -e "${GREEN}systemd服务已创建${NC}"
        echo -e "${YELLOW}使用以下命令管理服务:${NC}"
        echo "  启动: sudo systemctl start vpn-security-client"
        echo "  停止: sudo systemctl stop vpn-security-client"
        echo "  开机启动: sudo systemctl enable vpn-security-client"
    fi
}

# 显示使用说明
show_usage_info() {
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    echo ""
    echo -e "${GREEN}================================================================${NC}"
    echo -e "${GREEN}                    安装完成！${NC}"
    echo -e "${GREEN}================================================================${NC}"
    echo ""
    echo -e "${YELLOW}使用方法:${NC}"
    echo "  1. 运行主程序: sudo $script_dir/server_proxy.sh"
    echo "  2. 首次使用请先配置OpenVPN连接 (选项1)"
    echo "  3. 连接VPN (选项2)"
    echo "  4. 启用Kill Switch防护 (选项5)"
    echo "  5. 可选择启用全局代理模式 (选项8)"
    echo ""
    echo -e "${YELLOW}重要提示:${NC}"
    echo "  - 程序需要root权限运行"
    echo "  - 首次使用前请阅读README.md"
    echo "  - 建议在测试环境中先行验证"
    echo "  - 定期备份VPN配置文件"
    echo ""
    echo -e "${BLUE}文档位置: $script_dir/README.md${NC}"
    echo ""
}

# 主安装流程
main() {
    show_header
    
    echo -e "${BLUE}开始安装Linux VPN安全客户端...${NC}"
    echo ""
    
    # 检测系统
    detect_os
    echo ""
    
    # 检查权限
    check_root
    echo ""
    
    # 更新包管理器
    update_package_manager
    echo ""
    
    # 安装依赖
    if install_dependencies; then
        echo ""
    else
        echo -e "${RED}依赖安装失败${NC}"
        exit 1
    fi
    
    # 验证安装
    if verify_installation; then
        echo ""
    else
        echo -e "${RED}安装验证失败${NC}"
        exit 1
    fi
    
    # 设置权限
    setup_permissions
    echo ""
    
    # 创建系统服务 (可选)
    create_systemd_service
    
    # 显示使用说明
    show_usage_info
}

# 运行主程序
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
