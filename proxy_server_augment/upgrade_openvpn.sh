#!/bin/bash

# OpenVPN升级脚本
# 升级到支持block-outside-dns的版本

set -euo pipefail

# 设置脚本权限
chmod +x "$0" 2>/dev/null || true

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        echo -e "${RED}错误: 需要root权限来升级OpenVPN${NC}"
        echo -e "${YELLOW}请使用sudo运行此脚本: sudo $0${NC}"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        source /etc/os-release
        OS=$ID
        VERSION=$VERSION_ID
    elif [[ -f /etc/redhat-release ]]; then
        OS="centos"
    elif [[ -f /etc/debian_version ]]; then
        OS="debian"
    else
        echo -e "${RED}无法检测操作系统${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}检测到操作系统: $OS $VERSION${NC}"
}

# 版本比较函数
version_compare() {
    local version1="$1"
    local version2="$2"
    
    # 移除版本号中的非数字字符
    version1=$(echo "$version1" | sed 's/[^0-9.]//g')
    version2=$(echo "$version2" | sed 's/[^0-9.]//g')
    
    # 使用sort进行版本比较
    if [[ "$(printf '%s\n' "$version1" "$version2" | sort -V | head -n1)" == "$version2" ]]; then
        return 0  # version1 >= version2
    else
        return 1  # version1 < version2
    fi
}

# 检查当前OpenVPN版本
check_current_version() {
    if command -v openvpn >/dev/null 2>&1; then
        CURRENT_VERSION=$(openvpn --version | head -1 | awk '{print $2}')
        echo -e "${BLUE}当前OpenVPN版本: $CURRENT_VERSION${NC}"
        
        if version_compare "$CURRENT_VERSION" "2.6.0"; then
            echo -e "${GREEN}✓ 当前版本支持block-outside-dns${NC}"
            echo "无需升级"
            exit 0
        else
            echo -e "${YELLOW}当前版本不支持block-outside-dns，需要升级${NC}"
        fi
    else
        echo -e "${RED}OpenVPN未安装${NC}"
        CURRENT_VERSION=""
    fi
}

# 停止OpenVPN服务
stop_openvpn() {
    echo -e "${BLUE}停止OpenVPN服务...${NC}"
    
    # 停止systemd服务
    if systemctl is-active --quiet openvpn 2>/dev/null; then
        systemctl stop openvpn
        echo "已停止OpenVPN systemd服务"
    fi
    
    # 停止所有OpenVPN进程
    if pgrep -f openvpn >/dev/null; then
        pkill -f openvpn
        sleep 2
        echo "已停止OpenVPN进程"
    fi
}

# Ubuntu/Debian升级
upgrade_ubuntu() {
    echo -e "${BLUE}为Ubuntu/Debian升级OpenVPN...${NC}"
    
    # 移除旧版本
    apt-get remove -y openvpn || true
    
    # 安装必要的包
    apt-get update
    apt-get install -y software-properties-common apt-transport-https ca-certificates gnupg curl wget
    
    # 添加OpenVPN官方仓库
    curl -fsSL https://swupdate.openvpn.net/repos/openvpn-repo-pkg-key.pub | gpg --dearmor > /etc/apt/trusted.gpg.d/openvpn.gpg
    
    # 检测发行版代号
    local codename=$(lsb_release -sc 2>/dev/null || echo "jammy")
    echo "deb https://build.openvpn.net/debian/openvpn/stable $codename main" > /etc/apt/sources.list.d/openvpn.list
    
    # 更新并安装
    apt-get update
    apt-get install -y openvpn
}

# CentOS/RHEL升级
upgrade_centos() {
    echo -e "${BLUE}为CentOS/RHEL升级OpenVPN...${NC}"
    
    # 移除旧版本
    yum remove -y openvpn || true
    
    # 安装EPEL仓库
    if ! rpm -qa | grep -q epel-release; then
        yum install -y epel-release
    fi
    
    # 添加OpenVPN仓库
    cat > /etc/yum.repos.d/openvpn.repo << 'EOF'
[openvpn]
name=OpenVPN Repository
baseurl=https://build.openvpn.net/RHEL$releasever/$basearch/
enabled=1
gpgcheck=1
gpgkey=https://swupdate.openvpn.net/repos/openvpn-repo-pkg-key.pub
EOF
    
    # 安装
    yum install -y openvpn
}

# 从源码编译升级
upgrade_from_source() {
    echo -e "${BLUE}从源码编译升级OpenVPN...${NC}"
    
    local openvpn_version="2.6.12"
    local build_dir="/tmp/openvpn-upgrade"
    
    # 安装编译依赖
    case $OS in
        ubuntu|debian)
            apt-get install -y build-essential libssl-dev liblzo2-dev libpam0g-dev
            ;;
        centos|rhel)
            yum install -y gcc gcc-c++ openssl-devel lzo-devel pam-devel
            ;;
        fedora)
            dnf install -y gcc gcc-c++ openssl-devel lzo-devel pam-devel
            ;;
    esac
    
    # 创建构建目录
    rm -rf "$build_dir"
    mkdir -p "$build_dir"
    cd "$build_dir"
    
    # 下载源码
    echo "下载OpenVPN $openvpn_version 源码..."
    wget "https://swupdate.openvpn.org/community/releases/openvpn-${openvpn_version}.tar.gz"
    tar -xzf "openvpn-${openvpn_version}.tar.gz"
    cd "openvpn-${openvpn_version}"
    
    # 配置编译选项
    echo "配置编译选项..."
    ./configure --enable-iproute2 --enable-plugins --enable-plugin-auth-pam --enable-plugin-down-root
    
    # 编译
    echo "编译OpenVPN..."
    make -j$(nproc)
    
    # 安装
    echo "安装OpenVPN..."
    make install
    
    # 创建符号链接
    ln -sf /usr/local/sbin/openvpn /usr/sbin/openvpn
    
    # 清理构建目录
    cd /
    rm -rf "$build_dir"
}

# 验证升级
verify_upgrade() {
    echo -e "${BLUE}验证升级结果...${NC}"
    
    if command -v openvpn >/dev/null 2>&1; then
        local new_version=$(openvpn --version | head -1 | awk '{print $2}')
        echo -e "${GREEN}✓ OpenVPN升级成功，新版本: $new_version${NC}"
        
        if version_compare "$new_version" "2.6.0"; then
            echo -e "${GREEN}✓ 新版本支持block-outside-dns功能${NC}"
        else
            echo -e "${YELLOW}⚠ 新版本可能仍不支持block-outside-dns${NC}"
        fi
        
        return 0
    else
        echo -e "${RED}✗ OpenVPN升级失败${NC}"
        return 1
    fi
}

# 主函数
main() {
    echo -e "${BLUE}=== OpenVPN升级工具 ===${NC}"
    echo ""
    
    check_root
    detect_os
    check_current_version
    
    echo ""
    echo -e "${YELLOW}警告: 升级将停止当前的VPN连接${NC}"
    read -p "确认继续升级? (y/N): " confirm
    
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}升级已取消${NC}"
        exit 0
    fi
    
    stop_openvpn
    
    case $OS in
        ubuntu|debian)
            upgrade_ubuntu
            ;;
        centos|rhel)
            upgrade_centos
            ;;
        fedora)
            # Fedora通常有较新版本，尝试直接安装
            dnf upgrade -y openvpn || upgrade_from_source
            ;;
        *)
            echo -e "${YELLOW}不支持的操作系统，尝试从源码编译...${NC}"
            upgrade_from_source
            ;;
    esac
    
    verify_upgrade
    
    echo ""
    echo -e "${GREEN}=== 升级完成 ===${NC}"
    echo -e "${YELLOW}下一步:${NC}"
    echo "1. 重新配置VPN连接"
    echo "2. 测试block-outside-dns功能"
    echo "3. 运行 ./diagnose.sh 检查状态"
}

# 运行主程序
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
