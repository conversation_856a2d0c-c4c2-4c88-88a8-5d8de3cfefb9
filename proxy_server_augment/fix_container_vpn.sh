#!/bin/bash

# 容器环境VPN修复脚本
# 专门解决Docker容器中OpenVPN运行的问题

set -euo pipefail

# 设置脚本权限
chmod +x "$0" 2>/dev/null || true

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${BLUE}=== 容器环境VPN修复工具 ===${NC}"
echo ""

# 检查是否在容器中
if [[ ! -f /.dockerenv ]] && ! grep -q docker /proc/1/cgroup 2>/dev/null; then
    echo -e "${YELLOW}未检测到容器环境，此脚本专用于Docker容器${NC}"
    exit 0
fi

echo -e "${BLUE}检测到Docker容器环境${NC}"
echo ""

# 1. 检查和创建TUN设备
echo -e "${CYAN}1. 检查TUN设备${NC}"
if [[ -c /dev/net/tun ]]; then
    echo -e "${GREEN}✓ TUN设备已存在${NC}"
    ls -la /dev/net/tun
else
    echo -e "${YELLOW}TUN设备不存在，尝试创建...${NC}"
    
    # 创建设备目录
    mkdir -p /dev/net
    
    # 创建TUN设备
    if mknod /dev/net/tun c 10 200 2>/dev/null; then
        chmod 666 /dev/net/tun
        echo -e "${GREEN}✓ TUN设备创建成功${NC}"
        ls -la /dev/net/tun
    else
        echo -e "${RED}✗ 无法创建TUN设备${NC}"
        echo -e "${YELLOW}容器需要特权模式运行${NC}"
        echo -e "${YELLOW}启动命令示例: docker run --privileged ...${NC}"
        echo -e "${YELLOW}或者: docker run --cap-add=NET_ADMIN --device=/dev/net/tun ...${NC}"
    fi
fi
echo ""

# 2. 检查网络权限
echo -e "${CYAN}2. 检查网络权限${NC}"

# 测试网络接口创建权限
echo -n "测试网络接口权限: "
if ip link add test-dummy type dummy 2>/dev/null; then
    ip link delete test-dummy 2>/dev/null
    echo -e "${GREEN}✓ 有权限${NC}"
else
    echo -e "${RED}✗ 无权限${NC}"
    echo -e "${YELLOW}需要NET_ADMIN权限${NC}"
fi

# 测试路由表修改权限
echo -n "测试路由权限: "
if ip route show >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 有权限${NC}"
else
    echo -e "${RED}✗ 无权限${NC}"
fi

# 测试iptables权限
echo -n "测试防火墙权限: "
if iptables -L >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 有权限${NC}"
else
    echo -e "${RED}✗ 无权限${NC}"
    echo -e "${YELLOW}需要NET_ADMIN权限${NC}"
fi
echo ""

# 3. 检查OpenVPN版本
echo -e "${CYAN}3. 检查OpenVPN版本${NC}"
if command -v openvpn >/dev/null 2>&1; then
    version=$(openvpn --version | head -1 | awk '{print $2}')
    echo "OpenVPN版本: $version"
    
    # 检查版本是否支持block-outside-dns
    version_num=$(echo "$version" | sed 's/[^0-9.]//g')
    major=$(echo "$version_num" | cut -d. -f1)
    minor=$(echo "$version_num" | cut -d. -f2)
    
    if [[ $major -gt 2 ]] || [[ $major -eq 2 && $minor -ge 6 ]]; then
        echo -e "${GREEN}✓ 支持 block-outside-dns${NC}"
    else
        echo -e "${RED}✗ 不支持 block-outside-dns (需要 2.6.0+)${NC}"
        echo -e "${YELLOW}建议升级OpenVPN版本${NC}"
    fi
else
    echo -e "${RED}OpenVPN未安装${NC}"
fi
echo ""

# 4. 修复配置文件
echo -e "${CYAN}4. 修复OpenVPN配置${NC}"
config_dir="/workspace/proxy_server_aug/proxy_server_augment/configs/openvpn"

if [[ -d "$config_dir" ]]; then
    ovpn_files=($(find "$config_dir" -name "*.ovpn" 2>/dev/null))
    
    if [[ ${#ovpn_files[@]} -gt 0 ]]; then
        for config_file in "${ovpn_files[@]}"; do
            echo "处理配置: $(basename "$config_file")"
            
            # 创建备份
            cp "$config_file" "${config_file}.backup.$(date +%Y%m%d_%H%M%S)"
            
            # 添加容器环境适配配置
            if ! grep -q "# 容器环境适配" "$config_file"; then
                cat >> "$config_file" << 'EOF'

# 容器环境适配配置
script-security 2
route-up /bin/true
route-pre-down /bin/true
up /bin/true
down /bin/true
dev tun
dev-type tun
topology subnet
persist-key
persist-tun
# 忽略用户组设置（容器中可能不存在）
user nobody
group nogroup
EOF
                echo -e "  ${GREEN}✓ 已添加容器适配配置${NC}"
            else
                echo -e "  ${YELLOW}配置已存在${NC}"
            fi
        done
    else
        echo -e "${YELLOW}未找到OpenVPN配置文件${NC}"
    fi
else
    echo -e "${YELLOW}配置目录不存在${NC}"
fi
echo ""

# 5. 创建容器启动脚本
echo -e "${CYAN}5. 创建容器启动脚本${NC}"
cat > /usr/local/bin/container-vpn-start << 'EOF'
#!/bin/bash

# 容器VPN启动脚本

# 确保TUN设备存在
if [[ ! -c /dev/net/tun ]]; then
    mkdir -p /dev/net
    mknod /dev/net/tun c 10 200 2>/dev/null || true
    chmod 666 /dev/net/tun 2>/dev/null || true
fi

# 启动OpenVPN
if [[ $# -gt 0 ]]; then
    config_file="$1"
    if [[ -f "$config_file" ]]; then
        echo "启动OpenVPN: $config_file"
        openvpn --config "$config_file" --script-security 2 --daemon
    else
        echo "配置文件不存在: $config_file"
        exit 1
    fi
else
    echo "用法: $0 <config_file>"
    exit 1
fi
EOF

chmod +x /usr/local/bin/container-vpn-start
echo -e "${GREEN}✓ 容器启动脚本已创建: /usr/local/bin/container-vpn-start${NC}"
echo ""

# 6. 测试网络连接
echo -e "${CYAN}6. 测试基础网络连接${NC}"

echo -n "测试DNS解析: "
if dig +short google.com >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 正常${NC}"
else
    echo -e "${RED}✗ 失败${NC}"
fi

echo -n "测试外部连接: "
if curl -s --max-time 5 http://www.google.com >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 正常${NC}"
else
    echo -e "${RED}✗ 失败${NC}"
fi
echo ""

# 7. 提供使用建议
echo -e "${CYAN}7. 使用建议${NC}"
echo -e "${YELLOW}容器启动建议:${NC}"
echo "docker run --privileged \\"
echo "  --cap-add=NET_ADMIN \\"
echo "  --device=/dev/net/tun \\"
echo "  -v /workspace/proxy_server_aug:/workspace/proxy_server_aug \\"
echo "  your-image"
echo ""

echo -e "${YELLOW}或者使用完全特权模式:${NC}"
echo "docker run --privileged \\"
echo "  -v /workspace/proxy_server_aug:/workspace/proxy_server_aug \\"
echo "  your-image"
echo ""

echo -e "${YELLOW}VPN连接命令:${NC}"
echo "# 使用修复后的脚本"
echo "sudo ./quick_fix.sh"
echo ""
echo "# 或者直接启动VPN"
echo "container-vpn-start /path/to/config.ovpn"
echo ""

echo -e "${GREEN}=== 容器环境修复完成 ===${NC}"
echo ""
echo -e "${YELLOW}下一步:${NC}"
echo "1. 确保容器有足够权限"
echo "2. 运行 sudo ./quick_fix.sh 升级OpenVPN"
echo "3. 重新连接VPN"
echo "4. 运行 ./diagnose.sh 验证"
