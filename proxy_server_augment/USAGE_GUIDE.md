# Linux VPN安全客户端 - 使用指南

## 快速开始

### 1. 安装依赖
```bash
# 运行自动安装脚本
sudo ./install.sh

# 或手动安装 (Ubuntu/Debian)
sudo apt-get install openvpn iptables iproute2 dnsutils curl
```

### 2. 运行程序
```bash
# 启动主程序 (需要root权限)
sudo ./server_proxy.sh

# 或运行演示模式 (无需root权限)
./demo.sh
```

### 3. 基本配置流程
1. 选择选项 `1` - 配置OpenVPN连接
2. 选择选项 `2` - 连接VPN
3. 选择选项 `5` - 启用Kill Switch防护
4. 选择选项 `8` - 启用全局代理模式 (可选)

## 详细功能说明

### VPN连接管理 (选项1-4)

#### 选项1: 配置OpenVPN连接
支持三种配置方式：
- **导入.ovpn文件**: 直接导入现有的OpenVPN配置文件
- **手动输入配置**: 手动输入服务器信息创建配置
- **从URL下载**: 从网络URL下载配置文件

**示例配置过程**:
```
请选择配置方式:
1. 导入.ovpn配置文件
2. 手动输入服务器信息  
3. 从URL下载配置文件

请选择 [1-3]: 1
请输入.ovpn文件的完整路径: /path/to/your/config.ovpn
请输入配置名称 (默认: config): my-vpn
```

#### 选项2: 连接VPN
自动连接到配置的VPN服务器，包括：
- 启动OpenVPN进程
- 等待连接建立
- 验证连接状态
- 显示连接结果

#### 选项3: 断开VPN
安全断开VPN连接：
- 停止OpenVPN进程
- 清理网络配置
- 恢复原始路由

#### 选项4: 查看连接状态
显示详细的VPN连接信息：
- 进程状态
- 网络接口状态
- 路由信息
- OpenVPN状态文件内容

### 安全防护功能 (选项5-7)

#### 选项5: 启用Kill Switch
**Kill Switch是防IP泄漏的核心功能**：
- 自动检测VPN断开
- 立即阻止所有非VPN流量
- 只允许连接到VPN服务器的流量
- 保护本地网络访问

**工作原理**:
1. 备份当前防火墙规则
2. 设置严格的iptables规则
3. 只允许VPN接口和本地流量
4. 阻止所有其他出站连接

#### 选项6: 禁用Kill Switch
安全地禁用Kill Switch：
- 恢复原始防火墙规则
- 恢复正常网络访问
- 清理临时配置

#### 选项7: DNS泄漏检测
检测DNS配置安全性：
- 显示当前DNS服务器
- 测试DNS解析
- 检查是否存在DNS泄漏
- 提供安全建议

### 代理模式控制 (选项8-9)

#### 选项8: 启用全局代理模式
**最高级别的IP保护**：
- 强制所有流量通过VPN
- 修改系统路由表
- 确保没有流量绕过VPN
- 适用于需要最高安全性的场景

**注意事项**:
- 可能影响本地服务访问
- 需要VPN已连接
- 需要root权限

#### 选项9: 恢复智能代理模式
恢复正常的路由模式：
- 恢复原始路由表
- 允许本地流量直连
- 保持VPN连接
- 适用于日常使用

### 网络测试功能 (选项10-12)

#### 选项10: 延迟测试
测试到各大网站的网络延迟：
- Google (google.com)
- 腾讯 (qq.com)
- 百度 (baidu.com)
- 阿里巴巴 (alibaba.com)
- YouTube (youtube.com)
- Google搜索 (www.google.com)

#### 选项11: IP地址检测
通过多个服务检测当前外部IP：
- ipify API
- httpbin服务
- icanhazip服务
- 分析IP一致性
- 显示地理位置信息

#### 选项12: 完整安全检测
综合安全状态检查：
- VPN连接状态
- IP地址泄漏检测
- DNS泄漏检测
- IPv6泄漏检测
- WebRTC泄漏提醒
- 网络延迟测试

### 系统管理功能 (选项13-15)

#### 选项13: 查看日志
查看各种系统日志：
- VPN客户端日志
- OpenVPN连接日志
- 系统安全日志
- 综合日志视图

#### 选项14: 备份/恢复配置
配置文件管理：
- 创建配置备份
- 恢复历史配置
- 列出备份文件
- 删除旧备份

#### 选项15: 系统诊断
全面的系统状态检查：
- 系统信息
- 网络配置
- VPN状态
- 安全状态
- 防火墙状态
- 依赖检查

## 安全最佳实践

### 1. 基本安全配置
```bash
# 1. 连接VPN
sudo ./server_proxy.sh
# 选择: 2 (连接VPN)

# 2. 启用Kill Switch
# 选择: 5 (启用Kill Switch)

# 3. 检查安全状态
# 选择: 12 (完整安全检测)
```

### 2. 最高安全级别配置
```bash
# 1. 连接VPN
# 2. 启用Kill Switch
# 3. 启用全局代理模式
# 选择: 8 (启用全局代理模式)

# 4. 验证配置
# 选择: 11 (IP地址检测)
# 选择: 7 (DNS泄漏检测)
```

### 3. 日常使用配置
```bash
# 1. 连接VPN
# 2. 启用Kill Switch
# 3. 保持智能代理模式 (默认)
# 4. 定期检查: 选择12 (完整安全检测)
```

## 故障排除

### 常见问题及解决方案

#### 1. 权限错误
```
错误: 需要root权限
解决: sudo ./server_proxy.sh
```

#### 2. OpenVPN连接失败
```
检查项:
- 配置文件是否正确
- 网络连接是否正常
- 防火墙是否阻止连接
- 证书文件是否存在
```

#### 3. Kill Switch无法启用
```
检查项:
- 是否有root权限
- iptables是否可用
- VPN是否已连接
```

#### 4. DNS泄漏问题
```
解决步骤:
1. 选择选项7检测DNS泄漏
2. 启用DNS保护
3. 重启网络服务
4. 再次检测验证
```

### 日志分析
```bash
# 查看详细日志
sudo ./server_proxy.sh
# 选择: 13 (查看日志)

# 或直接查看日志文件
tail -f logs/vpn-client.log
tail -f logs/openvpn.log
```

## 高级用法

### 命令行模式 (计划中)
```bash
# 快速连接
sudo ./server_proxy.sh --connect config-name

# 启用Kill Switch
sudo ./server_proxy.sh --kill-switch on

# 检查状态
sudo ./server_proxy.sh --status
```

### 自动化脚本
```bash
#!/bin/bash
# 自动VPN连接脚本

# 启动VPN客户端
sudo ./server_proxy.sh --auto-connect

# 等待连接建立
sleep 10

# 启用安全防护
sudo ./server_proxy.sh --enable-security

# 验证连接
sudo ./server_proxy.sh --verify
```

## 技术支持

### 获取帮助
1. 查看README.md了解详细信息
2. 运行系统诊断: 选择选项15
3. 查看日志文件排查问题
4. 运行测试脚本验证功能

### 报告问题
提供以下信息：
- 操作系统版本
- 错误信息
- 日志文件内容
- 系统诊断结果

---

**注意**: 本工具涉及系统级网络配置，请在了解相关风险的情况下使用。建议在测试环境中先行验证。
