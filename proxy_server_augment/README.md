# Linux VPN安全客户端 v2.0

专业级防IP泄漏的OpenVPN客户端工具，提供类似Tunnelblick的安全保护功能。

## 功能特性

### 🔒 核心安全功能
- **Kill Switch防护** - 防止VPN断开时IP泄漏
- **DNS泄漏保护** - 强制使用安全DNS服务器
- **IPv6泄漏防护** - 自动禁用IPv6防止泄漏
- **防火墙集成** - 智能防火墙规则管理
- **全局代理模式** - 强制所有流量通过VPN

### 🌐 网络功能
- **OpenVPN支持** - 完整的OpenVPN客户端功能
- **多配置管理** - 支持多个VPN配置文件
- **智能路由** - 自动路由表管理
- **连接监控** - 实时VPN连接状态监控

### 🧪 测试验证
- **延迟测试** - 测试到Google、腾讯、百度等网站的延迟
- **IP地址检测** - 多服务IP地址验证
- **DNS泄漏检测** - 全面的DNS泄漏检查
- **安全审计** - 完整的安全状态检查

### 📊 管理功能
- **配置备份** - 自动配置备份和恢复
- **日志管理** - 详细的操作日志记录
- **系统诊断** - 全面的系统状态诊断

## 系统要求

### 必需依赖
- Linux操作系统 (Ubuntu 18.04+, CentOS 7+, Debian 9+)
- OpenVPN客户端
- iptables
- iproute2 (ip命令)
- dig (DNS查询工具)

### 可选依赖
- curl 或 wget (用于下载配置和IP检测)
- systemd-resolve (DNS缓存管理)

## 安装指南

### 1. 安装系统依赖

#### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install openvpn iptables iproute2 dnsutils curl
```

#### CentOS/RHEL
```bash
sudo yum install openvpn iptables iproute bind-utils curl
```

#### Fedora
```bash
sudo dnf install openvpn iptables iproute bind-utils curl
```

### 2. 下载和设置

```bash
# 克隆或下载项目
git clone <repository-url>
cd proxy_server_augment

# 设置执行权限
chmod +x server_proxy.sh modules/*.sh

# 运行程序
sudo ./server_proxy.sh
```

## 使用指南

### 首次使用

1. **启动程序**
   ```bash
   sudo ./server_proxy.sh
   ```

2. **配置OpenVPN连接**
   - 选择选项 `1` 配置OpenVPN连接
   - 可以导入.ovpn文件、手动配置或从URL下载

3. **连接VPN**
   - 选择选项 `2` 连接VPN
   - 程序会自动验证连接状态

4. **启用安全防护**
   - 选择选项 `5` 启用Kill Switch
   - 选择选项 `8` 启用全局代理模式（可选）

### 主要功能说明

#### VPN连接管理
- **选项1**: 配置OpenVPN连接 - 支持多种配置方式
- **选项2**: 连接VPN - 自动连接并验证
- **选项3**: 断开VPN - 安全断开连接
- **选项4**: 查看连接状态 - 详细的连接信息

#### 安全防护
- **选项5**: 启用Kill Switch - 防止断网时IP泄漏
- **选项6**: 禁用Kill Switch - 恢复正常网络访问
- **选项7**: DNS泄漏检测 - 检查DNS配置安全性

#### 代理模式
- **选项8**: 启用全局代理模式 - 所有流量强制通过VPN
- **选项9**: 恢复智能代理模式 - 恢复正常路由

#### 网络测试
- **选项10**: 延迟测试 - 测试到各大网站的延迟
- **选项11**: IP地址检测 - 验证当前外部IP
- **选项12**: 完整安全检测 - 全面的安全状态检查

## 安全特性详解

### Kill Switch机制
Kill Switch是防止IP泄漏的核心功能：
- 自动检测VPN断开
- 立即阻止所有非VPN流量
- 只允许连接到VPN服务器的流量
- 保护本地网络访问

### DNS泄漏防护
- 强制使用安全DNS服务器 (*******, *******)
- 防止DNS配置被覆盖
- 检测DNS查询是否通过VPN

### 全局代理模式
- 强制所有TCP流量通过VPN
- 修改系统路由表
- 确保没有流量绕过VPN
- 提供最高级别的IP保护

## 故障排除

### 常见问题

1. **权限错误**
   ```
   错误: 需要root权限
   解决: 使用sudo运行脚本
   ```

2. **OpenVPN连接失败**
   ```
   检查: 配置文件是否正确
   检查: 网络连接是否正常
   检查: 防火墙是否阻止连接
   ```

3. **Kill Switch无法启用**
   ```
   检查: 是否有root权限
   检查: iptables是否可用
   检查: VPN是否已连接
   ```

4. **DNS泄漏**
   ```
   启用: DNS泄漏保护
   检查: /etc/resolv.conf配置
   重启: 网络服务
   ```

### 日志查看
程序提供详细的日志记录：
- 选择选项 `13` 查看各种日志
- 日志文件位置: `logs/vpn-client.log`
- OpenVPN日志: `logs/openvpn.log`

### 系统诊断
选择选项 `15` 进行系统诊断，包括：
- 系统信息检查
- 网络配置验证
- VPN状态检查
- 安全状态评估
- 依赖检查

## 配置文件结构

```
proxy_server_augment/
├── server_proxy.sh          # 主程序
├── modules/                 # 功能模块
│   ├── openvpn_core.sh     # OpenVPN核心功能
│   ├── security_core.sh    # 安全防护功能
│   └── network_test.sh     # 网络测试功能
├── configs/                # 配置文件目录
│   └── openvpn/           # OpenVPN配置
├── logs/                   # 日志文件
├── backup/                 # 备份文件
└── README.md              # 说明文档
```

## 安全建议

1. **定期更新**: 保持系统和OpenVPN客户端更新
2. **配置验证**: 定期运行安全检测验证配置
3. **日志监控**: 定期检查日志文件
4. **备份配置**: 定期备份VPN配置
5. **测试连接**: 定期测试VPN连接和安全性

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 支持

如有问题或建议，请提交Issue或联系开发团队。

---

**警告**: 本工具涉及系统级网络配置，请在了解相关风险的情况下使用。建议在测试环境中先行验证。
