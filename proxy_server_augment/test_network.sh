#!/bin/bash

# 简化的网络测试脚本 - 用于验证VPN连接和延迟测试

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${BLUE}=== VPN网络连接测试 ===${NC}"
echo ""

# 1. 检查VPN进程
echo -e "${CYAN}1. 检查VPN进程状态${NC}"
if pgrep -f openvpn >/dev/null; then
    echo -e "${GREEN}✓ OpenVPN进程正在运行${NC}"
    
    # 显示进程信息
    echo "进程详情:"
    ps aux | grep openvpn | grep -v grep | head -1
else
    echo -e "${RED}✗ OpenVPN进程未运行${NC}"
fi
echo ""

# 2. 检查VPN接口
echo -e "${CYAN}2. 检查VPN网络接口${NC}"
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS - 检查所有utun接口
    echo "所有utun接口:"
    ifconfig | grep -A 3 "utun[0-9]"
    echo ""

    # 查找有IP地址的VPN接口
    vpn_interfaces=$(ifconfig | grep -B 1 "inet 10\." | grep "utun" | awk '{print $1}' | sed 's/://')
    if [[ -z "$vpn_interfaces" ]]; then
        # 如果没有10.x.x.x的地址，查找其他私有IP
        vpn_interfaces=$(ifconfig | grep -B 1 "inet 192\.168\." | grep "utun" | awk '{print $1}' | sed 's/://')
    fi
    if [[ -z "$vpn_interfaces" ]]; then
        # 查找172.16-31.x.x的地址
        vpn_interfaces=$(ifconfig | grep -B 1 "inet 172\." | grep "utun" | awk '{print $1}' | sed 's/://')
    fi

    if [[ -n "$vpn_interfaces" ]]; then
        echo -e "${GREEN}✓ 找到VPN接口:${NC}"
        for interface in $vpn_interfaces; do
            vpn_ip=$(ifconfig "$interface" | grep "inet " | awk '{print $2}')
            echo "  $interface: $vpn_ip"
        done
    else
        echo -e "${YELLOW}⚠ 未找到带IP地址的VPN接口，但VPN可能仍在工作${NC}"
    fi
else
    # Linux
    vpn_interfaces=$(ip link show | grep -E "tun[0-9]+|tap[0-9]+" | awk -F: '{print $2}' | tr -d ' ')
    if [[ -n "$vpn_interfaces" ]]; then
        echo -e "${GREEN}✓ 找到VPN接口:${NC}"
        for interface in $vpn_interfaces; do
            vpn_ip=$(ip addr show "$interface" | grep "inet " | awk '{print $2}')
            echo "  $interface: $vpn_ip"
        done
    else
        echo -e "${RED}✗ 未找到VPN接口${NC}"
    fi
fi
echo ""

# 3. 检查外部IP地址
echo -e "${CYAN}3. 检查当前外部IP地址${NC}"
external_ip=$(curl -s --max-time 10 https://api.ipify.org)
if [[ -n "$external_ip" ]]; then
    echo -e "${GREEN}✓ 当前外部IP: $external_ip${NC}"
else
    echo -e "${RED}✗ 无法获取外部IP地址${NC}"
fi
echo ""

# 4. 测试网络延迟
echo -e "${CYAN}4. 网络延迟测试${NC}"

# 测试目标列表
test_targets="Google:google.com Baidu:baidu.com QQ:qq.com YouTube:youtube.com"

for target_pair in $test_targets; do
    name=$(echo "$target_pair" | cut -d: -f1)
    target=$(echo "$target_pair" | cut -d: -f2)
    echo -n "测试 $name ($target): "

    # 使用适合当前系统的ping命令
    if command -v ping >/dev/null 2>&1; then
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            ping_result=$(ping -c 3 -t 5 "$target" 2>/dev/null)
        else
            # Linux
            ping_result=$(ping -c 3 -W 5 "$target" 2>/dev/null)
        fi

        if [[ $? -eq 0 ]]; then
            # 提取平均延迟
            avg_latency=$(echo "$ping_result" | tail -1 | awk -F '/' '{print $5}' | sed 's/[^0-9.]//g')
            if [[ -n "$avg_latency" ]]; then
                echo -e "${GREEN}${avg_latency}ms${NC}"
            else
                echo -e "${YELLOW}延迟数据解析失败${NC}"
            fi
        else
            echo -e "${RED}连接失败${NC}"
        fi
    else
        echo -e "${RED}ping命令不可用${NC}"
    fi
done
echo ""

# 5. 测试Google访问
echo -e "${CYAN}5. 测试Google访问${NC}"
if curl -I -s --max-time 10 https://www.google.com >/dev/null 2>&1; then
    echo -e "${GREEN}✓ Google访问正常${NC}"
else
    echo -e "${RED}✗ Google访问失败${NC}"
fi
echo ""

# 6. DNS测试
echo -e "${CYAN}6. DNS解析测试${NC}"
if command -v nslookup >/dev/null 2>&1; then
    dns_result=$(nslookup google.com 2>/dev/null | grep "Address:" | tail -1 | awk '{print $2}')
    if [[ -n "$dns_result" ]]; then
        echo -e "${GREEN}✓ DNS解析正常: google.com -> $dns_result${NC}"
    else
        echo -e "${RED}✗ DNS解析失败${NC}"
    fi
elif command -v dig >/dev/null 2>&1; then
    dns_result=$(dig +short google.com 2>/dev/null | head -1)
    if [[ -n "$dns_result" ]]; then
        echo -e "${GREEN}✓ DNS解析正常: google.com -> $dns_result${NC}"
    else
        echo -e "${RED}✗ DNS解析失败${NC}"
    fi
else
    echo -e "${YELLOW}⚠ DNS测试工具不可用${NC}"
fi
echo ""

echo -e "${BLUE}=== 测试完成 ===${NC}"
echo ""
echo -e "${YELLOW}总结:${NC}"
echo "- 如果VPN进程运行且有VPN接口，说明VPN连接正常"
echo "- 如果外部IP与您的真实IP不同，说明VPN正在工作"
echo "- 如果延迟测试显示数值，说明网络连接正常"
echo "- 如果Google访问正常，说明可以访问被墙网站"
