#!/bin/bash

# 演示脚本 - 展示VPN安全客户端的主要功能
# 注意：这是一个演示脚本，不需要实际的VPN连接

set -euo pipefail

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 显示标题
show_demo_header() {
    clear
    echo -e "${CYAN}================================================================${NC}"
    echo -e "${CYAN}           Linux VPN安全客户端 - 功能演示${NC}"
    echo -e "${CYAN}================================================================${NC}"
    echo ""
}

# 演示OpenVPN配置功能
demo_openvpn_config() {
    echo -e "${BLUE}=== 演示：OpenVPN配置功能 ===${NC}"
    echo ""
    
    # 设置环境变量
    export USER_CONFIG_DIR="/tmp/vpn_demo_$$"
    export CONFIGS_DIR="$SCRIPT_DIR/configs"
    export LOGS_DIR="$SCRIPT_DIR/logs"
    export BACKUP_DIR="$SCRIPT_DIR/backup"
    
    # 创建目录
    mkdir -p "$USER_CONFIG_DIR" "$CONFIGS_DIR/openvpn" "$LOGS_DIR" "$BACKUP_DIR"
    
    # 加载OpenVPN模块
    source "$SCRIPT_DIR/modules/openvpn_core.sh"
    
    echo -e "${YELLOW}1. 检查OpenVPN安装状态${NC}"
    if command -v openvpn >/dev/null 2>&1; then
        echo -e "${GREEN}✓ OpenVPN已安装${NC}"
    else
        echo -e "${YELLOW}⚠ OpenVPN未安装 (演示模式)${NC}"
    fi
    echo ""
    
    echo -e "${YELLOW}2. 创建示例配置文件${NC}"
    
    # 创建示例配置
    local demo_config="$CONFIGS_DIR/openvpn/demo.ovpn"
    cat > "$demo_config" << 'EOF'
client
dev tun
proto udp
remote demo.vpnserver.com 1194
resolv-retry infinite
nobind
persist-key
persist-tun
ca ca.crt
cert client.crt
key client.key
remote-cert-tls server
cipher AES-256-CBC
verb 3
EOF
    
    echo -e "${GREEN}✓ 示例配置文件已创建: demo.ovpn${NC}"
    echo ""
    
    echo -e "${YELLOW}3. 验证配置文件${NC}"
    if validate_ovpn_file "$demo_config"; then
        echo -e "${GREEN}✓ 配置文件验证通过${NC}"
    else
        echo -e "${RED}✗ 配置文件验证失败${NC}"
    fi
    echo ""
    
    echo -e "${YELLOW}4. 列出可用配置${NC}"
    list_openvpn_configs
    echo ""
    
    # 清理
    rm -rf "$USER_CONFIG_DIR"
    
    echo -e "${GREEN}OpenVPN配置功能演示完成${NC}"
    echo ""
}

# 演示安全防护功能
demo_security_features() {
    echo -e "${BLUE}=== 演示：安全防护功能 ===${NC}"
    echo ""
    
    # 设置环境变量
    export USER_CONFIG_DIR="/tmp/vpn_demo_$$"
    export CONFIGS_DIR="$SCRIPT_DIR/configs"
    export LOGS_DIR="$SCRIPT_DIR/logs"
    export BACKUP_DIR="$SCRIPT_DIR/backup"
    
    # 创建目录
    mkdir -p "$USER_CONFIG_DIR" "$LOGS_DIR" "$BACKUP_DIR"
    
    # 加载安全模块
    source "$SCRIPT_DIR/modules/security_core.sh"
    
    echo -e "${YELLOW}1. 初始化安全模块${NC}"
    init_security_module
    echo -e "${GREEN}✓ 安全模块初始化完成${NC}"
    echo ""
    
    echo -e "${YELLOW}2. 检查安全状态${NC}"
    check_security_status
    echo -e "${GREEN}✓ 安全状态检查完成${NC}"
    echo ""
    
    echo -e "${YELLOW}3. 防火墙功能检查${NC}"
    if command -v iptables >/dev/null 2>&1; then
        echo -e "${GREEN}✓ iptables可用${NC}"
        echo "当前iptables规则数量: $(iptables -L 2>/dev/null | wc -l || echo "无法获取")"
    else
        echo -e "${YELLOW}⚠ iptables不可用 (演示模式)${NC}"
    fi
    echo ""
    
    echo -e "${YELLOW}4. DNS配置检查${NC}"
    if [[ -f "/etc/resolv.conf" ]]; then
        echo -e "${GREEN}✓ DNS配置文件存在${NC}"
        echo "当前DNS服务器:"
        grep nameserver /etc/resolv.conf | head -3 || echo "无法读取DNS配置"
    else
        echo -e "${YELLOW}⚠ DNS配置文件不存在${NC}"
    fi
    echo ""
    
    # 清理
    rm -rf "$USER_CONFIG_DIR"
    
    echo -e "${GREEN}安全防护功能演示完成${NC}"
    echo ""
}

# 演示网络测试功能
demo_network_testing() {
    echo -e "${BLUE}=== 演示：网络测试功能 ===${NC}"
    echo ""
    
    # 设置环境变量
    export LOGS_DIR="$SCRIPT_DIR/logs"
    mkdir -p "$LOGS_DIR"
    
    # 加载网络测试模块
    source "$SCRIPT_DIR/modules/network_test.sh"
    
    echo -e "${YELLOW}1. 网络工具检查${NC}"
    local tools=("ping" "curl" "dig" "ip")
    for tool in "${tools[@]}"; do
        if command -v "$tool" >/dev/null 2>&1; then
            echo -e "${GREEN}✓ $tool 可用${NC}"
        else
            echo -e "${RED}✗ $tool 不可用${NC}"
        fi
    done
    echo ""
    
    echo -e "${YELLOW}2. 基本网络连接测试${NC}"
    if ping -c 1 -W 3 ******* >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 网络连接正常${NC}"
    else
        echo -e "${RED}✗ 网络连接异常${NC}"
    fi
    echo ""
    
    echo -e "${YELLOW}3. 延迟测试示例${NC}"
    echo "测试目标: google.com"
    local latency_result=$(test_single_latency "google.com" 2>/dev/null || echo "测试失败")
    if [[ "$latency_result" != "测试失败" ]]; then
        echo -e "${GREEN}延迟: $latency_result${NC}"
    else
        echo -e "${YELLOW}延迟测试失败 (可能是网络问题)${NC}"
    fi
    echo ""
    
    echo -e "${YELLOW}4. IP检测服务测试${NC}"
    if command -v curl >/dev/null 2>&1; then
        echo "尝试获取外部IP地址..."
        local external_ip=$(get_external_ip "https://api.ipify.org" 2>/dev/null || echo "获取失败")
        if [[ "$external_ip" != "获取失败" ]]; then
            echo -e "${GREEN}当前外部IP: $external_ip${NC}"
        else
            echo -e "${YELLOW}IP检测失败 (可能是网络问题)${NC}"
        fi
    else
        echo -e "${YELLOW}curl不可用，跳过IP检测${NC}"
    fi
    echo ""
    
    echo -e "${GREEN}网络测试功能演示完成${NC}"
    echo ""
}

# 演示系统诊断功能
demo_system_diagnosis() {
    echo -e "${BLUE}=== 演示：系统诊断功能 ===${NC}"
    echo ""
    
    echo -e "${YELLOW}1. 系统信息${NC}"
    echo "操作系统: $(uname -s)"
    echo "内核版本: $(uname -r)"
    echo "架构: $(uname -m)"
    echo ""
    
    echo -e "${YELLOW}2. 网络接口${NC}"
    if command -v ip >/dev/null 2>&1; then
        echo "活跃的网络接口:"
        ip link show | grep "state UP" | head -5 || echo "无法获取接口信息"
    else
        echo "ip命令不可用"
    fi
    echo ""
    
    echo -e "${YELLOW}3. 依赖检查${NC}"
    local required_commands=("ping" "ip" "iptables" "dig" "curl")
    for cmd in "${required_commands[@]}"; do
        if command -v "$cmd" >/dev/null 2>&1; then
            echo -e "${GREEN}✓ $cmd${NC}"
        else
            echo -e "${RED}✗ $cmd (缺失)${NC}"
        fi
    done
    echo ""
    
    echo -e "${GREEN}系统诊断演示完成${NC}"
    echo ""
}

# 主演示流程
main() {
    show_demo_header
    
    echo -e "${BLUE}这是Linux VPN安全客户端的功能演示${NC}"
    echo -e "${YELLOW}演示将展示各个模块的主要功能，无需实际VPN连接${NC}"
    echo ""
    
    read -p "按回车键开始演示..."
    echo ""
    
    # 运行各项演示
    demo_openvpn_config
    read -p "按回车键继续下一个演示..."
    echo ""
    
    demo_security_features
    read -p "按回车键继续下一个演示..."
    echo ""
    
    demo_network_testing
    read -p "按回车键继续下一个演示..."
    echo ""
    
    demo_system_diagnosis
    
    echo -e "${GREEN}================================================================${NC}"
    echo -e "${GREEN}                    演示完成！${NC}"
    echo -e "${GREEN}================================================================${NC}"
    echo ""
    echo -e "${YELLOW}要使用完整功能，请运行:${NC}"
    echo "  sudo $SCRIPT_DIR/server_proxy.sh"
    echo ""
    echo -e "${YELLOW}要安装系统依赖，请运行:${NC}"
    echo "  sudo $SCRIPT_DIR/install.sh"
    echo ""
}

# 运行演示
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
