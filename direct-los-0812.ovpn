# OpenVPN客户端配置
# 服务器: ************:1194
# 实例: direct-los-0812
# 生成时间: Tue Aug 12 03:54:50 AM EDT 2025

client
dev tun
proto udp
remote ************ 1194
resolv-retry infinite
nobind
persist-key
persist-tun
remote-cert-tls server
auth SHA512
cipher AES-256-GCM
ignore-unknown-option block-outside-dns
block-outside-dns
verb 3

<ca>
-----BEGIN CERTIFICATE-----
MIIDSzCCAjOgAwIBAgIUf/L0XABD7EITcHQanU054j3g190wDQYJKoZIhvcNAQEL
BQAwFjEUMBIGA1UEAwwLRWFzeS1SU0EgQ0EwHhcNMjUwODEyMDc1NDQ3WhcNMzUw
ODEwMDc1NDQ3WjAWMRQwEgYDVQQDDAtFYXN5LVJTQSBDQTCCASIwDQYJKoZIhvcN
AQEBBQADggEPADCCAQoCggEBALjAO86YlEZC6nHagZt7wnQZq7TJGhkA7qfwvPyl
41RUPgsY7ia6wYirOFkwdmH44AcFTN70zCPgt5Gcrx7sx8NJH9OieyEf3Ux1WEO1
pmmsCNGkTvjYHSgvUBcaBMd5aPccNr0YR1/WSn3SoUennWN7VbB3ar1iIX8DR+Wk
UPlbGHFb+m5stjLR3XM6B0xIF+QKyeboOnpO5YtowrFWsC11sofhR2JqF/Z6yzLz
7YaVIegw7qOOkmmyIaFDSrOvIPRJ/EhFG98CoBG1qB46V102b+ax2PKi3obZbezE
90zKchK3h0EfSw32WBb9LfgKHOhOK0PBAt0XnsHxRuDlrBECAwEAAaOBkDCBjTAM
BgNVHRMEBTADAQH/MB0GA1UdDgQWBBQszxmhZI3zBcnW2DEPV4cqUUaE+jBRBgNV
HSMESjBIgBQszxmhZI3zBcnW2DEPV4cqUUaE+qEapBgwFjEUMBIGA1UEAwwLRWFz
eS1SU0EgQ0GCFH/y9FwAQ+xCE3B0Gp1NOeI94NfdMAsGA1UdDwQEAwIBBjANBgkq
hkiG9w0BAQsFAAOCAQEAUyuWSW7vxb3PX5fVrweOc4kuniCRW/jvDIltWxXNQ+uz
Xz+uocuGTthZgbM5qGnEjSybUxppsi7T83Osk5E7hS6Ta65AaIQNPtznl0ZQ9Sa4
syW7XCtlgaDY1Z5CtYyUTWcri+0sKWl3JDlTxNp8QRMr5kXvIzikam0Bs2NRxxIn
QIMCNt/jPkbf18O82ef3gOdGw9i+uPkjLoiqgIlpJ9tERazasuSmtKGcTi30v34S
kpyZHkSC53V2RjpYsp9hLg/ihZBceyZ8gKHq0zqSKxNz4hsyjMMepROCbCgccRrh
3cYjImI1XHQecNaAX331RUsJYotqx2e2bpua5msN3Q==
-----END CERTIFICATE-----
</ca>

<cert>
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number:
            d9:56:81:b3:5e:e1:26:7d:47:fd:c8:44:3d:5d:5b:ef
        Signature Algorithm: sha256WithRSAEncryption
        Issuer: CN=Easy-RSA CA
        Validity
            Not Before: Aug 12 07:54:49 2025 GMT
            Not After : Aug 10 07:54:49 2035 GMT
        Subject: CN=client-direct-los-0812
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (2048 bit)
                Modulus:
                    00:c5:ff:83:63:4c:e1:5a:0a:05:7c:70:42:34:18:
                    9a:0b:ef:12:ce:b3:a5:00:16:f4:b5:c6:80:05:47:
                    49:45:7f:07:72:50:c6:1c:25:9c:53:1c:4c:c4:77:
                    af:58:cc:cb:55:41:a1:ba:b0:52:98:3a:c4:b1:cc:
                    51:49:66:92:8d:27:6d:89:86:04:52:95:96:a1:ea:
                    0d:97:0b:81:54:dd:bc:ee:f4:20:01:74:ac:c1:4e:
                    90:d3:41:b2:15:25:05:00:cd:66:60:3e:d8:28:46:
                    d2:dd:8b:b7:4e:1b:1d:ae:72:f7:f2:f4:07:39:ad:
                    4f:62:95:22:ec:bb:d8:fc:b7:d9:80:0d:da:42:f6:
                    cb:d8:3a:39:92:4b:86:20:95:f5:d2:0c:4e:4b:15:
                    63:8d:10:61:39:ab:ae:c9:21:e0:23:3e:5d:cf:fc:
                    5e:ea:85:0f:c7:89:3c:5b:d7:37:16:36:39:bc:69:
                    3c:67:e3:a7:e2:d9:a5:41:11:2c:1f:65:c5:c6:82:
                    df:9f:d0:ab:1a:8a:c7:cc:2c:2e:c4:67:e5:d0:52:
                    69:51:15:79:cb:89:a8:db:d2:97:ce:2c:41:70:ac:
                    cf:cb:4e:4b:a6:ca:cd:17:ca:ff:7b:f9:cc:87:6f:
                    57:b8:dc:3b:b2:84:42:98:6f:7c:b0:7e:86:e5:a1:
                    21:67
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Basic Constraints: 
                CA:FALSE
            X509v3 Subject Key Identifier: 
                13:F8:60:5F:C9:46:89:42:C9:A9:D3:5A:7C:29:B3:C5:9B:EC:64:B5
            X509v3 Authority Key Identifier: 
                keyid:2C:CF:19:A1:64:8D:F3:05:C9:D6:D8:31:0F:57:87:2A:51:46:84:FA
                DirName:/CN=Easy-RSA CA
                serial:7F:F2:F4:5C:00:43:EC:42:13:70:74:1A:9D:4D:39:E2:3D:E0:D7:DD
            X509v3 Extended Key Usage: 
                TLS Web Client Authentication
            X509v3 Key Usage: 
                Digital Signature
    Signature Algorithm: sha256WithRSAEncryption
    Signature Value:
        1c:0c:06:f5:b3:68:5a:af:6f:41:02:e1:41:85:14:6f:be:83:
        2d:41:d3:99:38:df:db:6a:44:0f:d9:46:1e:3f:f7:b0:69:f2:
        83:7f:af:d8:3a:81:cb:d5:93:08:b9:cf:18:36:9b:3a:8b:49:
        ca:f8:9a:f3:93:19:ec:73:0f:3d:b4:18:83:99:a0:f6:31:dc:
        29:75:15:c6:86:46:4e:9f:08:65:41:b7:4d:f9:24:57:95:fe:
        25:a4:24:ce:a6:db:1d:4b:f8:17:9d:b2:db:90:79:fe:ac:3a:
        83:9d:96:b8:56:be:ef:ae:89:06:2d:64:a9:39:7f:35:5d:5c:
        36:9a:eb:af:9f:d4:b7:5c:3c:3a:fd:aa:47:dd:0f:df:20:7c:
        9b:10:4e:98:b9:3d:fe:b8:ef:3f:86:4b:3d:f6:d8:d2:d8:c6:
        f4:ea:d0:86:0f:2a:71:0f:f3:6a:41:86:9b:0d:33:fb:86:e6:
        33:26:b8:c4:19:e6:21:71:6f:b9:96:16:8f:71:c3:03:87:bc:
        55:73:f4:94:e8:a4:a3:7c:1f:ab:b6:58:34:0d:38:29:f7:e8:
        85:99:5f:bd:a5:7d:1c:a8:78:20:33:73:b9:5f:0c:64:8b:7a:
        cf:52:79:4c:07:ba:9c:6d:04:78:3d:7a:00:88:0a:52:15:a2:
        60:f4:32:12
-----BEGIN CERTIFICATE-----
MIIDZTCCAk2gAwIBAgIRANlWgbNe4SZ9R/3IRD1dW+8wDQYJKoZIhvcNAQELBQAw
FjEUMBIGA1UEAwwLRWFzeS1SU0EgQ0EwHhcNMjUwODEyMDc1NDQ5WhcNMzUwODEw
MDc1NDQ5WjAhMR8wHQYDVQQDDBZjbGllbnQtZGlyZWN0LWxvcy0wODEyMIIBIjAN
BgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxf+DY0zhWgoFfHBCNBiaC+8SzrOl
ABb0tcaABUdJRX8HclDGHCWcUxxMxHevWMzLVUGhurBSmDrEscxRSWaSjSdtiYYE
UpWWoeoNlwuBVN287vQgAXSswU6Q00GyFSUFAM1mYD7YKEbS3Yu3ThsdrnL38vQH
Oa1PYpUi7LvY/LfZgA3aQvbL2Do5kkuGIJX10gxOSxVjjRBhOauuySHgIz5dz/xe
6oUPx4k8W9c3FjY5vGk8Z+On4tmlQREsH2XFxoLfn9CrGorHzCwuxGfl0FJpURV5
y4mo29KXzixBcKzPy05LpsrNF8r/e/nMh29XuNw7soRCmG98sH6G5aEhZwIDAQAB
o4GiMIGfMAkGA1UdEwQCMAAwHQYDVR0OBBYEFBP4YF/JRolCyanTWnwps8Wb7GS1
MFEGA1UdIwRKMEiAFCzPGaFkjfMFydbYMQ9XhypRRoT6oRqkGDAWMRQwEgYDVQQD
DAtFYXN5LVJTQSBDQYIUf/L0XABD7EITcHQanU054j3g190wEwYDVR0lBAwwCgYI
KwYBBQUHAwIwCwYDVR0PBAQDAgeAMA0GCSqGSIb3DQEBCwUAA4IBAQAcDAb1s2ha
r29BAuFBhRRvvoMtQdOZON/bakQP2UYeP/ewafKDf6/YOoHL1ZMIuc8YNps6i0nK
+Jrzkxnscw89tBiDmaD2MdwpdRXGhkZOnwhlQbdN+SRXlf4lpCTOptsdS/gXnbLb
kHn+rDqDnZa4Vr7vrokGLWSpOX81XVw2muuvn9S3XDw6/apH3Q/fIHybEE6YuT3+
uO8/hks99tjS2Mb06tCGDypxD/NqQYabDTP7huYzJrjEGeYhcW+5lhaPccMDh7xV
c/SU6KSjfB+rtlg0DTgp9+iFmV+9pX0cqHggM3O5Xwxki3rPUnlMB7qcbQR4PXoA
iApSFaJg9DIS
-----END CERTIFICATE-----
</cert>

<key>
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************</key>

<tls-crypt>
# Easy-RSA TLS-CRYPT Key
#
# 2048 bit OpenVPN static key
#
-----BEGIN OpenVPN Static key V1-----
7cb7f8ca423f57d63611b334edab6b12
f9cc0b0e15b5b763319ab3aac81c9686
79d1337c60fb3516cdd2e54e5a91eb5d
f011c4da889329d166e7d9d4ea8fb79a
e0e0bf73123f9d741c24d2ebc27c4167
caaa784adcf3fa7fa946304f625a0780
628a634a6509705b19bd6ea1ee890275
90ae299aad2615905349c0de541ac9ef
7d0a811221ff111332d510487637536c
594817ae2e985e21728800a680e4d9dd
7cd2df0a16fcd72fddbc20a23e85782f
5af1bae90a91367d33551e4bd1cf8f8c
3d78aeaadf14360043be1645211a84da
8b93c329a3213382b21adb0d59779f0f
068ff24a3a88c890492e048f531238bb
97b36967dfa7146a274dd899e05d46d6
-----END OpenVPN Static key V1-----
</tls-crypt>
