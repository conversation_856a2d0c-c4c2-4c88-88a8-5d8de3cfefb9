# OpenVPN客户端配置
# 服务器: *************:1194
# 实例: direct-newyork-0812
# 生成时间: Tue Aug 12 08:18:08 AM UTC 2025

client
dev tun
proto udp
remote ************* 1194
resolv-retry infinite
nobind
persist-key
persist-tun
remote-cert-tls server
auth SHA512
cipher AES-256-GCM
ignore-unknown-option block-outside-dns
block-outside-dns
verb 3

<ca>
-----BEGIN CERTIFICATE-----
MIIDSzCCAjOgAwIBAgIUZ5PRh/gyIOLvEyKqhkLHgHG2hzAwDQYJKoZIhvcNAQEL
BQAwFjEUMBIGA1UEAwwLRWFzeS1SU0EgQ0EwHhcNMjUwODEyMDgxODA2WhcNMzUw
ODEwMDgxODA2WjAWMRQwEgYDVQQDDAtFYXN5LVJTQSBDQTCCASIwDQYJKoZIhvcN
AQEBBQADggEPADCCAQoCggEBAKRD7GfVZZMVm571NOoClEl+vYJTiIp4Zxr4u0vV
BCNMgGDbfBN/ZVmswMIz1d1zrw/3i0qW4RjIZOsvfoPV7CxpRdG5f2+UEylo7dSB
ZPEuAbqwc9AhefegEqfey/86xS/oQtPPdfi5GyZArmi83TZcbZ/IkseyDlV3W1dG
If5L5hVCaKtlHopOSD99tVu56HYHpojYKx6V7j++oeB+8axB7ns2EXIJJMParVyX
PO3tRPjnXmUuCkaxeCPTMNjC+tCRj1U/eqs3FBHhYS+ZWxptkvbZ5Atyjjr0jbEa
bT81OrD9e6RkWJpDLbgTQBPzSXJG9GNgiiwlB/ZvysFbP50CAwEAAaOBkDCBjTAM
BgNVHRMEBTADAQH/MB0GA1UdDgQWBBRKcYWfa/k/a151Bs5ymPOSOkCjJzBRBgNV
HSMESjBIgBRKcYWfa/k/a151Bs5ymPOSOkCjJ6EapBgwFjEUMBIGA1UEAwwLRWFz
eS1SU0EgQ0GCFGeT0Yf4MiDi7xMiqoZCx4BxtocwMAsGA1UdDwQEAwIBBjANBgkq
hkiG9w0BAQsFAAOCAQEAM2z/bV8v5uOldOBrv6UIz8SCS73EprvMhz81YCghYPSr
2NO0LdnkStfUxF4SLfqRIjF51FwoSqzfCD61yCNHi37MW97moE3mMC9B9q0GeaWr
u00QiCro47Ee8wEHMvcwBOGSS/x9oJmBmx2sOrcHiQ3NS5P/hvabylQ+vl0Vjeg4
YsgNO2HCv3VKxpLXcRkKWacc7hsJf/bHgp5D/o8GWY7hAmFuRMX0sELZMafE1hZH
RIrMi14DqY9FSojPp5FR6jr5kAv7prjWrWTw1oRHcvfRd07L2E43u+pYMPFSA4Po
sOCB7fVIHt+BcC1ZSkVtaZbzQLOjEZYd9Q6v7yGcmg==
-----END CERTIFICATE-----
</ca>

<cert>
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number:
            35:dd:66:fc:8f:fb:66:d2:77:df:32:8e:75:4e:a6:7d
        Signature Algorithm: sha256WithRSAEncryption
        Issuer: CN=Easy-RSA CA
        Validity
            Not Before: Aug 12 08:18:07 2025 GMT
            Not After : Aug 10 08:18:07 2035 GMT
        Subject: CN=client-direct-newyork-0812
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (2048 bit)
                Modulus:
                    00:ba:80:ba:a8:a0:9f:ce:7c:b0:44:a0:11:09:0a:
                    8a:ad:72:9c:3c:90:9e:58:f0:d6:1f:a5:38:7d:92:
                    c4:cd:78:a3:c7:6e:86:63:65:a4:27:86:76:b4:70:
                    6e:d7:7c:e1:eb:c0:d2:f4:21:b9:cd:ea:6c:4f:52:
                    51:f2:1f:49:9f:13:f2:72:2f:05:ed:12:fc:69:4f:
                    2a:88:52:dd:ae:12:34:31:bb:6d:2f:7a:d0:40:80:
                    78:ce:ea:ed:9f:f1:9b:f3:87:66:86:25:21:89:58:
                    6a:09:34:7f:37:19:67:55:c4:c1:af:82:3c:88:99:
                    d5:3d:2c:e8:d6:cd:1a:43:2d:55:e0:b0:97:d7:c6:
                    af:68:e6:bd:74:ad:32:34:95:e8:66:03:60:7e:32:
                    90:80:2d:76:80:79:7f:61:e5:f4:9e:40:56:de:23:
                    85:47:ef:b1:d4:31:08:cb:39:f7:af:27:5c:ab:57:
                    82:5c:10:97:97:6c:68:0d:22:e5:9a:ee:d3:43:16:
                    71:94:d1:6a:c0:3b:06:b6:08:00:92:60:64:67:e9:
                    2d:74:7a:cf:96:f8:25:4f:e1:51:a5:5d:13:76:f9:
                    be:ae:43:11:b6:07:8c:75:07:4b:96:fd:15:70:d2:
                    33:90:37:3d:2f:70:89:2b:7b:05:e5:ec:58:8f:f6:
                    77:a3
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Basic Constraints: 
                CA:FALSE
            X509v3 Subject Key Identifier: 
                B5:40:D8:E4:6A:E5:C1:13:8C:79:CD:E0:1A:15:A0:3C:34:AA:74:5A
            X509v3 Authority Key Identifier: 
                keyid:4A:71:85:9F:6B:F9:3F:6B:5E:75:06:CE:72:98:F3:92:3A:40:A3:27
                DirName:/CN=Easy-RSA CA
                serial:67:93:D1:87:F8:32:20:E2:EF:13:22:AA:86:42:C7:80:71:B6:87:30
            X509v3 Extended Key Usage: 
                TLS Web Client Authentication
            X509v3 Key Usage: 
                Digital Signature
    Signature Algorithm: sha256WithRSAEncryption
    Signature Value:
        a2:4a:7f:82:9a:c4:89:5f:a4:7c:40:e6:03:b5:cb:54:9a:84:
        c1:8e:d0:d3:07:91:e6:04:06:9a:7f:68:fc:f6:3d:41:e4:f4:
        2a:0f:3a:07:d8:2c:09:08:ca:ad:c6:1b:ac:79:23:72:c5:90:
        31:a5:66:3a:a2:4e:00:f6:3f:e4:a6:6c:a4:e2:72:2b:1a:94:
        ea:73:b5:10:36:f7:ed:1d:ed:7f:a8:37:2e:b2:8d:10:0d:c4:
        64:cc:b5:e5:91:05:98:f9:ef:24:bb:b6:76:6c:eb:d8:58:4f:
        10:4c:81:82:78:c3:68:7b:e6:66:68:62:2a:d0:15:36:2e:0c:
        df:98:1d:29:43:65:1a:8e:79:3d:78:cf:3c:79:3d:42:a1:56:
        cc:bd:07:eb:8b:27:40:9c:30:87:50:bc:e4:63:3c:55:a4:31:
        6a:0f:2f:74:6e:e0:96:72:63:6c:a9:a9:08:e6:ad:6a:24:1a:
        68:87:73:27:f9:b8:27:c6:4e:d9:f7:d7:92:15:b4:ab:27:c8:
        ee:40:2c:f0:2f:88:20:53:be:0e:59:af:80:58:f9:d7:82:e9:
        73:5b:c4:34:ff:91:1b:49:90:8f:95:8e:a4:00:0f:10:c2:45:
        7f:1e:3b:85:0e:c6:82:e6:48:c4:80:51:00:d7:3b:5c:47:fc:
        aa:bf:99:36
-----BEGIN CERTIFICATE-----
MIIDaDCCAlCgAwIBAgIQNd1m/I/7ZtJ33zKOdU6mfTANBgkqhkiG9w0BAQsFADAW
MRQwEgYDVQQDDAtFYXN5LVJTQSBDQTAeFw0yNTA4MTIwODE4MDdaFw0zNTA4MTAw
ODE4MDdaMCUxIzAhBgNVBAMMGmNsaWVudC1kaXJlY3QtbmV3eW9yay0wODEyMIIB
IjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuoC6qKCfznywRKARCQqKrXKc
PJCeWPDWH6U4fZLEzXijx26GY2WkJ4Z2tHBu13zh68DS9CG5zepsT1JR8h9JnxPy
ci8F7RL8aU8qiFLdrhI0MbttL3rQQIB4zurtn/Gb84dmhiUhiVhqCTR/NxlnVcTB
r4I8iJnVPSzo1s0aQy1V4LCX18avaOa9dK0yNJXoZgNgfjKQgC12gHl/YeX0nkBW
3iOFR++x1DEIyzn3rydcq1eCXBCXl2xoDSLlmu7TQxZxlNFqwDsGtggAkmBkZ+kt
dHrPlvglT+FRpV0Tdvm+rkMRtgeMdQdLlv0VcNIzkDc9L3CJK3sF5exYj/Z3owID
AQABo4GiMIGfMAkGA1UdEwQCMAAwHQYDVR0OBBYEFLVA2ORq5cETjHnN4BoVoDw0
qnRaMFEGA1UdIwRKMEiAFEpxhZ9r+T9rXnUGznKY85I6QKMnoRqkGDAWMRQwEgYD
VQQDDAtFYXN5LVJTQSBDQYIUZ5PRh/gyIOLvEyKqhkLHgHG2hzAwEwYDVR0lBAww
CgYIKwYBBQUHAwIwCwYDVR0PBAQDAgeAMA0GCSqGSIb3DQEBCwUAA4IBAQCiSn+C
msSJX6R8QOYDtctUmoTBjtDTB5HmBAaaf2j89j1B5PQqDzoH2CwJCMqtxhuseSNy
xZAxpWY6ok4A9j/kpmyk4nIrGpTqc7UQNvftHe1/qDcuso0QDcRkzLXlkQWY+e8k
u7Z2bOvYWE8QTIGCeMNoe+ZmaGIq0BU2LgzfmB0pQ2Uajnk9eM88eT1CoVbMvQfr
iydAnDCHULzkYzxVpDFqDy90buCWcmNsqakI5q1qJBpoh3Mn+bgnxk7Z99eSFbSr
J8juQCzwL4ggU74OWa+AWPnXgulzW8Q0/5EbSZCPlY6kAA8QwkV/HjuFDsaC5kjE
gFEA1ztcR/yqv5k2
-----END CERTIFICATE-----
</cert>

<key>
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************</key>

<tls-crypt>
# Easy-RSA TLS-CRYPT Key
#
# 2048 bit OpenVPN static key
#
-----BEGIN OpenVPN Static key V1-----
26555c4c3bc773d79f60b0ead9ae46e0
fee656ebd420232072bcf3a63f2cb5a0
1e0b227b5021ae62b895c122691beca5
085065ab972d884c95c54446f9ac511b
874449342c56f87a88a99a5a73ee3602
83781968273999e547f6f10358668f27
0d0d4f56f74a4139b03c8ffd56944f50
acd9ab64e82cc3bdee57f69713aff441
47b67dd00d40aee80cfb79dc0d8c14b7
9aa915312ea17faf9c3c8760d38a4be0
60d88be74fe191c4e20b3bfa1b1fcd2e
0e6cded02fe072ac0c8ae65b7638f7f5
aaedcbd33b6839709b619ff9582d0153
2545cdd8f83e6a9fd56de0d7bbd9b587
04361e430a7dcb3b96f7c3fc030baaea
acd506c77ceef915899330fd114c754c
-----END OpenVPN Static key V1-----
</tls-crypt>
